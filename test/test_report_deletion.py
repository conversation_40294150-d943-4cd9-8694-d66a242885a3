#!/usr/bin/env python3
"""
专门测试报告删除功能
检查删除后列表仍显示的问题
"""

import os
import sys
import time
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup
import re

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class ReportDeletionTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def login_admin(self):
        """尝试登录管理员账户"""
        print("🔐 尝试登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        if not self.csrf_token:
            print("❌ 无法获取CSRF token")
            return False
            
        # 尝试使用默认管理员账户登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123'
        }

        # 添加CSRF token到表单数据
        if self.csrf_token:
            login_data['csrf_token'] = self.csrf_token

        response = self.session.post(login_url, data=login_data, allow_redirects=True)

        print(f"登录响应状态码: {response.status_code}")
        print(f"最终URL: {response.url}")

        # 检查是否登录成功
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            page_title = soup.title.string if soup.title else "No title"
            print(f"页面标题: {page_title}")

            # 如果页面标题包含"仪表板"或"dashboard"，说明登录成功
            if 'dashboard' in response.url.lower() or '仪表板' in page_title or 'dashboard' in page_title.lower():
                print("✅ 管理员登录成功")
                return True
            # 如果还是登录页面，说明登录失败
            elif soup.find('form') and soup.find('input', {'type': 'password'}):
                print("❌ 登录失败 - 仍在登录页面")
                # 检查是否有错误消息
                error_msgs = soup.find_all(class_=re.compile(r'alert|error'))
                for msg in error_msgs:
                    print(f"  错误信息: {msg.get_text().strip()}")
                return False
            else:
                print("✅ 管理员登录成功（根据页面内容判断）")
                return True
        else:
            print(f"❌ 登录失败，状态码: {response.status_code}")
            return False
            
    def check_reports_page(self):
        """检查报告管理页面"""
        print("\n📋 检查报告管理页面...")
        
        reports_url = urljoin(self.base_url, '/admin/reports')
        response = self.session.get(reports_url)
        
        if response.status_code != 200:
            print(f"❌ 无法访问报告管理页面，状态码: {response.status_code}")
            return None
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 检查页面结构
        print(f"页面标题: {soup.title.string if soup.title else 'No title'}")

        # 检查是否有错误信息
        error_messages = soup.find_all(class_=re.compile(r'alert|error'))
        if error_messages:
            print("⚠️ 页面包含错误信息:")
            for msg in error_messages:
                print(f"  - {msg.get_text().strip()}")

        # 检查表格
        table = soup.find('table')
        if not table:
            print("❌ 报告管理页面没有找到表格")

            # 检查是否有空状态提示
            empty_state = soup.find(text=re.compile(r'暂无|没有|empty', re.I))
            if empty_state:
                print("ℹ️ 找到空状态提示")

            # 输出页面的主要内容用于调试
            main_content = soup.find('main') or soup.find('div', class_=re.compile(r'content|main'))
            if main_content:
                print("📄 页面主要内容:")
                print(main_content.get_text()[:500] + "..." if len(main_content.get_text()) > 500 else main_content.get_text())

            return None
            
        # 查找报告行
        report_rows = table.find_all('tr')[1:]  # 跳过表头
        print(f"📊 找到 {len(report_rows)} 个报告")
        
        # 检查删除按钮
        delete_buttons = soup.find_all('button', {'onclick': re.compile(r'deleteReport')})
        print(f"🗑️ 找到 {len(delete_buttons)} 个删除按钮")
        
        # 分析删除按钮的JavaScript
        for i, button in enumerate(delete_buttons):
            onclick = button.get('onclick', '')
            data_report_id = button.get('data-report-id')
            data_project_name = button.get('data-project-name')
            
            print(f"删除按钮 {i+1}:")
            print(f"  onclick: {onclick}")
            print(f"  data-report-id: {data_report_id}")
            print(f"  data-project-name: {data_project_name}")
            
        return delete_buttons
        
    def check_javascript_syntax(self):
        """检查JavaScript语法"""
        print("\n🔧 检查JavaScript语法...")
        
        reports_url = urljoin(self.base_url, '/admin/reports')
        response = self.session.get(reports_url)
        
        if response.status_code != 200:
            print("❌ 无法获取页面内容")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找script标签
        scripts = soup.find_all('script')
        
        for i, script in enumerate(scripts):
            if script.string:
                js_content = script.string
                print(f"\n📜 Script {i+1}:")
                
                # 检查deleteReport函数
                if 'deleteReport' in js_content:
                    print("✅ 找到deleteReport函数")
                    
                    # 检查函数参数
                    if 'function deleteReport(button)' in js_content:
                        print("✅ deleteReport函数使用button参数")
                    elif 'function deleteReport(' in js_content:
                        print("⚠️ deleteReport函数参数可能不正确")
                        
                    # 检查data属性获取
                    if 'getAttribute(\'data-report-id\')' in js_content:
                        print("✅ 使用data-report-id属性")
                    else:
                        print("❌ 未使用data-report-id属性")
                        
                    if 'getAttribute(\'data-project-name\')' in js_content:
                        print("✅ 使用data-project-name属性")
                    else:
                        print("❌ 未使用data-project-name属性")
                        
                # 检查CSRF token函数
                if 'getCSRFToken' in js_content:
                    print("✅ 找到getCSRFToken函数")
                else:
                    print("❌ 未找到getCSRFToken函数")
                    
        return True
        
    def test_delete_api(self):
        """测试删除API端点"""
        print("\n🔌 测试删除API端点...")
        
        # 测试删除API（使用假的report_id）
        test_report_id = "test-report-id"
        delete_url = urljoin(self.base_url, f'/admin/reports/{test_report_id}/delete')
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': self.csrf_token or ''
        }
        
        response = self.session.post(delete_url, headers=headers)
        
        print(f"删除API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"删除API响应: {data}")
                return True
            except:
                print("删除API响应不是JSON格式")
                return False
        elif response.status_code == 404:
            print("✅ 删除API端点存在（报告不存在返回404是正常的）")
            return True
        else:
            print(f"❌ 删除API可能有问题，状态码: {response.status_code}")
            return False
            
    def check_database_operations(self):
        """检查数据库操作"""
        print("\n🗄️ 检查数据库操作...")
        
        try:
            # 导入模型
            from app.models.research_report import ResearchReport
            from app.services.database import db_service
            
            print(f"数据库服务状态: {'Mock模式' if db_service.use_mock else '真实数据库'}")
            
            # 测试数据库连接
            connection_ok = db_service.test_connection()
            print(f"数据库连接: {'✅ 正常' if connection_ok else '❌ 失败'}")
            
            # 测试获取报告列表
            try:
                reports, total = ResearchReport.get_all_reports(page=1, per_page=10)
                print(f"✅ 成功获取报告列表，共 {total} 个报告")
                
                if reports:
                    print("报告列表:")
                    for report in reports[:3]:  # 只显示前3个
                        print(f"  - ID: {report.get('id')}, 项目: {report.get('project_name')}")
                        
            except Exception as e:
                print(f"❌ 获取报告列表失败: {e}")
                
            # 测试删除操作（使用假ID）
            try:
                result = ResearchReport.delete("fake-id")
                print(f"删除操作测试: {'✅ 方法可调用' if result is not None else '❌ 方法调用失败'}")
            except Exception as e:
                print(f"❌ 删除操作测试失败: {e}")
                
        except Exception as e:
            print(f"❌ 数据库检查失败: {e}")
            
    def run_full_test(self):
        """运行完整的删除功能测试"""
        print("🚀 开始报告删除功能测试...")
        print(f"测试目标: {self.base_url}")
        
        # 检查服务器连接
        try:
            response = self.session.get(self.base_url, timeout=5)
            if response.status_code != 200:
                print(f"❌ 服务器不可访问，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            return False
            
        print("✅ 服务器连接正常")
        
        # 运行各项检查
        login_success = self.login_admin()
        
        if login_success:
            self.check_reports_page()
            self.check_javascript_syntax()
            self.test_delete_api()
            
        self.check_database_operations()
        
        print("\n📋 测试总结:")
        print("1. 检查管理员登录功能")
        print("2. 检查报告管理页面结构")
        print("3. 检查JavaScript语法和函数")
        print("4. 测试删除API端点")
        print("5. 检查数据库操作")
        
        return True

if __name__ == "__main__":
    # 检查服务器是否运行
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = ReportDeletionTest()
    success = tester.run_full_test()
    
    sys.exit(0 if success else 1)
