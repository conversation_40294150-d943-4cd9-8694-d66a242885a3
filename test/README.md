# 测试目录

本目录包含项目研究报告平台的所有测试代码。

## 目录结构

```
test/
├── __init__.py                    # Python包初始化文件
├── conftest.py                    # 测试配置和工具函数
├── run_all_tests.py              # 统一测试运行器
├── test_app.py                   # 基础应用测试
├── test_admin_templates.py       # 管理员模板测试
├── test_csrf_fix.py              # CSRF修复测试
├── test_deployment.py            # 部署验证测试
├── test_final_verification.py    # 最终验证测试
├── test_supabase.py              # Supabase连接测试
├── test_vercel_fix.py            # Vercel部署修复测试
└── README.md                     # 本文件
```

## 测试模块说明

### 🧪 test_app.py
**基础应用测试**
- 数据库连接测试
- 应用启动测试
- 文件结构检查
- 环境配置验证
- 目录结构检查
- 示例数据验证

### 🎨 test_admin_templates.py
**管理员模板测试**
- 管理员模板文件存在性检查
- 管理员路由测试
- 登录功能测试
- 模板渲染测试
- 文件上传验证
- 创建报告表单测试
- 数据库模型测试

### 🔒 test_csrf_fix.py
**CSRF修复测试**
- CSRF配置测试
- Serverless环境CSRF测试
- 表单提交测试
- 安全令牌验证

### 🚀 test_deployment.py
**部署验证测试**
- 关键文件存在性检查
- 模板语法验证
- 应用导入测试
- Vercel配置验证
- 环境变量检查
- 管理员路由存在性验证

### ✅ test_final_verification.py
**最终验证测试**
- 所有管理员模板渲染测试
- 路由保护验证
- 公共路由测试
- 数据库模型完整性检查
- 文件处理器测试
- 安全功能验证
- 错误处理测试

### 🗄️ test_supabase.py
**Supabase连接测试**
- Supabase连接验证
- 环境变量检查
- 数据库操作测试
- 表结构验证

### ☁️ test_vercel_fix.py
**Vercel部署修复测试**
- Serverless环境应用创建
- 文件系统限制测试
- 存储服务测试
- 环境变量配置测试

## 运行测试

### 运行所有测试
```bash
# 从项目根目录运行
python test/run_all_tests.py
```

### 运行单个测试模块
```bash
# 从项目根目录运行
python test/test_app.py
python test/test_deployment.py
python test/test_final_verification.py
```

### 快速验证部署准备
```bash
# 运行关键测试
python test/test_deployment.py
python test/test_final_verification.py
```

## 测试配置

### 环境变量
测试需要以下环境变量（在 `.env` 文件中配置）：
```
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_anon_key
FLASK_SECRET_KEY=your_secret_key_here
```

### 测试数据
- 测试使用模拟数据，不会影响生产数据
- 部分测试需要真实的Supabase连接
- 文件上传测试使用临时文件

## 测试结果解读

### ✅ 全部通过
- 应用已准备好部署
- 所有功能正常工作
- 可以安全部署到Vercel

### ❌ 部分失败
- 检查失败的测试模块
- 根据错误信息修复问题
- 重新运行测试验证修复

### 常见问题

1. **模板文件缺失**
   - 检查 `templates/admin/` 目录下的文件
   - 确保所有必需的模板都存在

2. **数据库连接失败**
   - 检查 `.env` 文件中的Supabase配置
   - 验证网络连接

3. **路由测试失败**
   - 检查 `app/views/` 目录下的路由定义
   - 确保蓝图正确注册

4. **导入错误**
   - 检查Python路径配置
   - 确保所有依赖已安装

## 添加新测试

### 创建新测试模块
1. 在 `test/` 目录下创建 `test_新功能.py`
2. 导入必要的模块和配置
3. 编写测试函数
4. 在 `run_all_tests.py` 中添加新模块

### 测试模板
```python
#!/usr/bin/env python3
"""
新功能测试
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_new_feature():
    """测试新功能"""
    print("测试新功能...")
    
    try:
        # 测试代码
        print("✓ 新功能正常")
        return True
    except Exception as e:
        print(f"✗ 新功能测试失败: {e}")
        return False

def run_tests():
    """运行测试"""
    print("=" * 50)
    print("新功能测试")
    print("=" * 50)
    
    tests = [
        ("新功能", test_new_feature)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
    
    print(f"\n测试结果: {passed}/{total} 通过")
    return passed == total

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
```

## 持续集成

测试可以集成到CI/CD流程中：
- GitHub Actions
- Vercel部署前检查
- 本地开发验证

建议在每次代码提交前运行完整测试套件。
