#!/usr/bin/env python3
"""
测试真实的报告删除功能
"""

import os
import sys
import time
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class RealDeletionTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def login_admin(self):
        """登录管理员账户"""
        print("🔐 登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        if not self.csrf_token:
            print("❌ 无法获取CSRF token")
            return False
            
        # 登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': self.csrf_token
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if 'dashboard' in response.url.lower():
            print("✅ 管理员登录成功")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
            
    def get_reports_list(self):
        """获取报告列表"""
        print("\n📋 获取报告列表...")
        
        reports_url = urljoin(self.base_url, '/admin/reports')
        response = self.session.get(reports_url)
        
        if response.status_code != 200:
            print(f"❌ 无法访问报告管理页面，状态码: {response.status_code}")
            return []
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找所有删除按钮
        delete_buttons = soup.find_all('button', {'onclick': lambda x: x and 'deleteReport' in x})
        
        reports = []
        for button in delete_buttons:
            report_id = button.get('data-report-id')
            project_name = button.get('data-project-name')
            if report_id and project_name:
                reports.append({
                    'id': report_id,
                    'name': project_name
                })
        
        print(f"✅ 找到 {len(reports)} 个报告:")
        for i, report in enumerate(reports, 1):
            print(f"  {i}. {report['name']} (ID: {report['id']})")
            
        return reports
        
    def test_delete_report(self, report_id, project_name):
        """测试删除指定报告"""
        print(f"\n🗑️ 测试删除报告: {project_name} (ID: {report_id})")
        
        # 更新CSRF token
        reports_url = urljoin(self.base_url, '/admin/reports')
        response = self.session.get(reports_url)
        self.csrf_token = self.get_csrf_token(reports_url)
        
        # 发送删除请求
        delete_url = urljoin(self.base_url, f'/admin/reports/{report_id}/delete')
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': self.csrf_token or ''
        }
        
        print(f"发送删除请求到: {delete_url}")
        print(f"CSRF Token: {self.csrf_token[:20]}..." if self.csrf_token else "无CSRF Token")
        
        response = self.session.post(delete_url, headers=headers)
        
        print(f"删除响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"删除响应: {data}")
                
                if data.get('success'):
                    print("✅ 删除成功")
                    return True
                else:
                    print(f"❌ 删除失败: {data.get('message', '未知错误')}")
                    return False
            except:
                print("❌ 删除响应不是JSON格式")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ 删除请求失败，状态码: {response.status_code}")
            return False
            
    def verify_deletion(self, report_id):
        """验证报告是否真的被删除"""
        print(f"\n🔍 验证报告是否被删除...")
        
        # 重新获取报告列表
        reports = self.get_reports_list()
        
        # 检查报告是否还存在
        for report in reports:
            if report['id'] == report_id:
                print(f"❌ 报告仍然存在于列表中: {report['name']}")
                return False
                
        print("✅ 报告已从列表中移除")
        
        # 检查数据库中是否还存在
        try:
            from app.models.research_report import ResearchReport
            
            report = ResearchReport.get_by_id(report_id)
            if report:
                print(f"❌ 报告仍然存在于数据库中")
                return False
            else:
                print("✅ 报告已从数据库中删除")
                return True
                
        except Exception as e:
            print(f"⚠️ 无法验证数据库状态: {e}")
            return True  # 假设删除成功
            
    def run_test(self):
        """运行完整的删除测试"""
        print("🚀 开始真实报告删除功能测试...")
        
        # 登录
        if not self.login_admin():
            return False
            
        # 获取报告列表
        reports = self.get_reports_list()
        if not reports:
            print("❌ 没有找到可删除的报告")
            return False
            
        # 选择第一个报告进行删除测试
        test_report = reports[0]
        print(f"\n🎯 选择测试报告: {test_report['name']}")
        
        # 记录删除前的报告数量
        before_count = len(reports)
        print(f"删除前报告数量: {before_count}")
        
        # 执行删除
        delete_success = self.test_delete_report(test_report['id'], test_report['name'])
        
        if delete_success:
            # 验证删除结果
            time.sleep(1)  # 等待一秒确保数据库更新
            deletion_verified = self.verify_deletion(test_report['id'])
            
            if deletion_verified:
                print("\n🎉 删除功能测试完全成功！")
                print("✅ 删除API响应成功")
                print("✅ 报告从列表中移除")
                print("✅ 报告从数据库中删除")
                return True
            else:
                print("\n⚠️ 删除功能部分成功")
                print("✅ 删除API响应成功")
                print("❌ 但报告仍然存在")
                return False
        else:
            print("\n❌ 删除功能测试失败")
            return False

if __name__ == "__main__":
    # 检查服务器是否运行
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = RealDeletionTest()
    success = tester.run_test()
    
    sys.exit(0 if success else 1)
