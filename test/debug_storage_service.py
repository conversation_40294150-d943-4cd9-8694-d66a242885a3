#!/usr/bin/env python3
"""
调试存储服务
"""

import os
import sys
import tempfile

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def debug_storage_service():
    """调试存储服务"""
    print("🔍 调试存储服务...")
    
    # 检查存储服务配置
    print("\n1. 检查存储服务配置...")
    try:
        from app.services.storage_service import storage_service
        from app.services.database import db_service
        
        print(f"存储服务类型: {'Supabase' if storage_service.use_supabase_storage else '本地存储'}")
        print(f"数据库服务类型: {'Mock' if db_service.use_mock else 'Supabase'}")
        print(f"环境变量 VERCEL: {os.getenv('VERCEL', 'None')}")
        print(f"环境变量 USE_SUPABASE_STORAGE: {os.getenv('USE_SUPABASE_STORAGE', 'None')}")
        
    except Exception as e:
        print(f"❌ 检查存储服务配置失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 测试文件保存（在Flask应用上下文中）
    print("\n2. 测试文件保存...")
    try:
        from werkzeug.datastructures import FileStorage
        from flask import Flask
        import io
        
        # 创建临时Flask应用
        app = Flask(__name__)
        app.config['UPLOAD_FOLDER'] = '/tmp/test_uploads'
        app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024
        
        with app.app_context():
            # 创建测试文件
            md_content = "# Debug Storage Test\n\nThis is a storage test."
            
            md_file = FileStorage(
                stream=io.BytesIO(md_content.encode('utf-8')),
                filename='debug_storage_test.md',
                content_type='text/markdown'
            )
            
            print(f"测试文件: {md_file.filename}")
            print(f"文件大小: {len(md_content)} bytes")
            
            # 测试存储服务
            try:
                file_path = storage_service.save_file(md_file, 'reports')
                print(f"✅ 文件保存成功: {file_path}")
            except Exception as e:
                print(f"❌ 文件保存失败: {e}")
                import traceback
                traceback.print_exc()
                
    except Exception as e:
        print(f"❌ 测试文件保存失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 检查数据库连接和权限
    print("\n3. 检查数据库连接和权限...")
    try:
        from app.services.database import db_service
        
        print(f"数据库连接状态: {'✅' if db_service.test_connection() else '❌'}")
        
        # 测试service key权限
        try:
            result = db_service.execute_query(
                'admin_users',
                'select',
                use_service_key=True
            )
            print(f"Service key权限测试: {'✅' if result.data else '❌'}")
        except Exception as e:
            print(f"Service key权限测试失败: {e}")
            
        # 测试普通权限
        try:
            result = db_service.execute_query(
                'admin_users',
                'select',
                use_service_key=False
            )
            print(f"普通权限测试: {'✅' if result.data else '❌'}")
        except Exception as e:
            print(f"普通权限测试失败: {e}")
            
    except Exception as e:
        print(f"❌ 检查数据库权限失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 检查Supabase客户端配置
    print("\n4. 检查Supabase客户端配置...")
    try:
        from app.services.database import db_service
        
        if hasattr(db_service, 'client') and db_service.client:
            print("✅ Supabase客户端已初始化")
            
            # 检查存储桶
            if hasattr(db_service.client, 'storage'):
                print("✅ Supabase存储客户端可用")
                
                # 尝试列出存储桶
                try:
                    buckets = db_service.client.storage.list_buckets()
                    print(f"存储桶列表: {[bucket.name for bucket in buckets] if buckets else '无'}")
                except Exception as e:
                    print(f"⚠️ 无法列出存储桶: {e}")
            else:
                print("❌ Supabase存储客户端不可用")
        else:
            print("❌ Supabase客户端未初始化")
            
    except Exception as e:
        print(f"❌ 检查Supabase客户端失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_storage_service()
