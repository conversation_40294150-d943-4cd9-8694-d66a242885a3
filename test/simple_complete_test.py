#!/usr/bin/env python3
"""
简单的完成请求测试
"""

import os
import sys
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup
import tempfile

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def simple_complete_test():
    """简单的完成请求测试"""
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # 登录管理员
    print("🔐 登录管理员...")
    login_url = urljoin(base_url, '/admin/login')
    response = session.get(login_url)
    
    soup = BeautifulSoup(response.text, 'html.parser')
    csrf_meta = soup.find('meta', {'name': 'csrf-token'})
    csrf_token = csrf_meta.get('content') if csrf_meta else None
    
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123',
        'csrf_token': csrf_token
    }
    
    response = session.post(login_url, data=login_data, allow_redirects=True)
    
    if 'dashboard' not in response.url.lower():
        print("❌ 登录失败")
        return
        
    print("✅ 登录成功")
    
    # 创建测试请求
    print("\n📝 创建测试请求...")
    try:
        from app.models.user_request import UserRequest
        
        request_data = {
            'user_email': '<EMAIL>',
            'project_name': 'Simple Test',
            'official_website': 'https://example.com',
            'status': 'pending'
        }
        
        result = UserRequest.create(request_data)
        
        if not result:
            print("❌ 创建测试请求失败")
            return
            
        request_id = result.get('id')
        print(f"✅ 测试请求创建成功，ID: {request_id}")
        
    except Exception as e:
        print(f"❌ 创建测试请求时出错: {e}")
        return
    
    # 创建最小的测试文件
    print("\n📁 创建测试文件...")
    md_content = "# Simple Test\nThis is a simple test."
    html_content = "<html><body>Simple Test</body></html>"
    
    md_file = tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8')
    md_file.write(md_content)
    md_file.close()
    
    html_file = tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8')
    html_file.write(html_content)
    html_file.close()
    
    print(f"MD文件: {md_file.name}")
    print(f"HTML文件: {html_file.name}")
    
    # 测试完成请求API
    print(f"\n🚀 测试完成请求API...")
    
    # 更新CSRF token
    requests_url = urljoin(base_url, '/admin/requests')
    response = session.get(requests_url)
    soup = BeautifulSoup(response.text, 'html.parser')
    csrf_meta = soup.find('meta', {'name': 'csrf-token'})
    csrf_token = csrf_meta.get('content') if csrf_meta else None
    
    complete_url = urljoin(base_url, f'/admin/requests/{request_id}/complete')
    
    try:
        files = {
            'report_file': ('simple.md', open(md_file.name, 'rb'), 'text/markdown'),
            'analysis_file': ('simple.html', open(html_file.name, 'rb'), 'text/html')
        }
        
        data = {
            'creator_name': 'Simple Test Admin',
            'description': '简单测试',
            'admin_notes': '简单测试备注'
        }
        
        headers = {
            'X-CSRFToken': csrf_token or ''
        }
        
        print(f"发送请求到: {complete_url}")
        print(f"数据: {data}")
        print(f"文件: {list(files.keys())}")
        
        response = session.post(complete_url, files=files, data=data, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print("✅ 完成请求成功！")
                else:
                    print(f"❌ 完成请求失败: {result.get('error')}")
            except:
                print("❌ 响应不是JSON格式")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    finally:
        # 清理临时文件
        try:
            os.unlink(md_file.name)
            os.unlink(html_file.name)
        except:
            pass

if __name__ == "__main__":
    simple_complete_test()
