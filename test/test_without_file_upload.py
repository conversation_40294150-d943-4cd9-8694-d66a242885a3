#!/usr/bin/env python3
"""
测试不包含文件上传的完成请求功能
"""

import os
import sys
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_without_file_upload():
    """测试不包含文件上传的完成请求功能"""
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # 登录管理员
    print("🔐 登录管理员...")
    login_url = urljoin(base_url, '/admin/login')
    response = session.get(login_url)
    
    soup = BeautifulSoup(response.text, 'html.parser')
    csrf_meta = soup.find('meta', {'name': 'csrf-token'})
    csrf_token = csrf_meta.get('content') if csrf_meta else None
    
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123',
        'csrf_token': csrf_token
    }
    
    response = session.post(login_url, data=login_data, allow_redirects=True)
    
    if 'dashboard' not in response.url.lower():
        print("❌ 登录失败")
        return
        
    print("✅ 登录成功")
    
    # 创建测试请求
    print("\n📝 创建测试请求...")
    try:
        from app.models.user_request import UserRequest
        
        request_data = {
            'user_email': '<EMAIL>',
            'project_name': 'No File Test',
            'official_website': 'https://example.com',
            'status': 'pending'
        }
        
        result = UserRequest.create(request_data)
        
        if not result:
            print("❌ 创建测试请求失败")
            return
            
        request_id = result.get('id')
        print(f"✅ 测试请求创建成功，ID: {request_id}")
        
    except Exception as e:
        print(f"❌ 创建测试请求时出错: {e}")
        return
    
    # 测试不带文件的完成请求API
    print(f"\n🚀 测试不带文件的完成请求API...")
    
    # 更新CSRF token
    requests_url = urljoin(base_url, '/admin/requests')
    response = session.get(requests_url)
    soup = BeautifulSoup(response.text, 'html.parser')
    csrf_meta = soup.find('meta', {'name': 'csrf-token'})
    csrf_token = csrf_meta.get('content') if csrf_meta else None
    
    complete_url = urljoin(base_url, f'/admin/requests/{request_id}/complete')
    
    # 只发送表单数据，不发送文件
    data = {
        'creator_name': 'No File Test Admin',
        'description': '不带文件的测试',
        'admin_notes': '不带文件的测试备注'
    }
    
    headers = {
        'X-CSRFToken': csrf_token or ''
    }
    
    print(f"发送请求到: {complete_url}")
    print(f"数据: {data}")
    
    response = session.post(complete_url, data=data, headers=headers)
    
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 400:
        print("✅ 正确返回400错误（缺少文件）")
        
        # 解析错误信息
        try:
            result = response.json()
            error_msg = result.get('error', '')
            if '文件' in error_msg:
                print("✅ 错误信息正确指出缺少文件")
            else:
                print(f"⚠️ 错误信息: {error_msg}")
        except:
            print("⚠️ 响应不是JSON格式")
            
    elif response.status_code == 500:
        print("❌ 返回500错误，可能有其他问题")
        try:
            result = response.json()
            print(f"错误详情: {result.get('details', '无详情')}")
        except:
            print("无法解析错误详情")
    else:
        print(f"⚠️ 意外的响应状态码: {response.status_code}")

if __name__ == "__main__":
    test_without_file_upload()
