#!/usr/bin/env python3
"""
测试用户请求查看详情功能
"""

import os
import sys
import time
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class ViewRequestDetailsTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def login_admin(self):
        """登录管理员账户"""
        print("🔐 登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        
        # 登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': self.csrf_token
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if 'dashboard' in response.url.lower():
            print("✅ 管理员登录成功")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
            
    def get_request_list(self):
        """获取请求列表"""
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            return []
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        requests_data = []
        for row in soup.find_all('tr')[1:]:  # 跳过表头
            cells = row.find_all('td')
            if len(cells) >= 5:
                project_name = cells[0].get_text().strip()
                user_email = cells[1].get_text().strip()
                status = cells[2].get_text().strip()
                created_at = cells[3].get_text().strip()
                
                # 提取request_id
                buttons = row.find_all('button')
                request_id = None
                for button in buttons:
                    onclick = button.get('onclick', '')
                    if 'viewRequest' in onclick:
                        import re
                        match = re.search(r"viewRequest\('([^']+)'\)", onclick)
                        if match:
                            request_id = match.group(1)
                            break
                
                if request_id:
                    requests_data.append({
                        'id': request_id,
                        'project_name': project_name,
                        'user_email': user_email,
                        'status': status,
                        'created_at': created_at
                    })
        
        return requests_data
        
    def test_request_details_api(self, request_id):
        """测试请求详情API"""
        print(f"\n🔍 测试请求详情API (ID: {request_id})")
        
        # 更新CSRF token
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        self.csrf_token = self.get_csrf_token(requests_url)
        
        # 调用详情API
        details_url = urljoin(self.base_url, f'/admin/requests/{request_id}')
        
        headers = {
            'X-CSRFToken': self.csrf_token or ''
        }
        
        response = self.session.get(details_url, headers=headers)
        
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"API响应格式: {'✅ JSON' if isinstance(result, dict) else '❌ 非JSON'}")
                
                if result.get('success'):
                    data = result.get('data', {})
                    print(f"✅ API调用成功")
                    print(f"返回数据字段:")
                    for key, value in data.items():
                        if isinstance(value, str) and len(value) > 50:
                            print(f"  {key}: {value[:50]}...")
                        else:
                            print(f"  {key}: {value}")
                    
                    # 验证必要字段
                    required_fields = ['id', 'project_name', 'user_email', 'status']
                    missing_fields = [field for field in required_fields if field not in data]
                    
                    if missing_fields:
                        print(f"❌ 缺少必要字段: {missing_fields}")
                        return False
                    else:
                        print("✅ 包含所有必要字段")
                        return True
                        
                else:
                    print(f"❌ API返回失败: {result.get('error', '未知错误')}")
                    return False
                    
            except json.JSONDecodeError:
                print("❌ API响应不是有效的JSON")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    def test_frontend_modal(self):
        """测试前端模态框功能"""
        print(f"\n🖥️ 测试前端模态框功能")
        
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            print("❌ 无法访问请求管理页面")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 检查模态框是否存在
        modal = soup.find('div', {'id': 'requestModal'})
        if modal:
            print("✅ 找到请求详情模态框")
        else:
            print("❌ 未找到请求详情模态框")
            return False
            
        # 检查viewRequest函数是否存在
        scripts = soup.find_all('script')
        view_request_found = False
        for script in scripts:
            if script.string and 'function viewRequest' in script.string:
                view_request_found = True
                print("✅ 找到viewRequest函数")
                break
                
        if not view_request_found:
            print("❌ 未找到viewRequest函数")
            return False
            
        # 检查查看按钮是否存在
        view_buttons = soup.find_all('button', {'onclick': lambda x: x and 'viewRequest' in x})
        if view_buttons:
            print(f"✅ 找到 {len(view_buttons)} 个查看按钮")
        else:
            print("❌ 未找到查看按钮")
            return False
            
        return True
        
    def run_complete_test(self):
        """运行完整的查看详情功能测试"""
        print("🚀 开始用户请求查看详情功能测试...")
        
        # 登录
        if not self.login_admin():
            return False
            
        # 获取请求列表
        requests_data = self.get_request_list()
        if not requests_data:
            print("❌ 没有找到任何请求数据")
            return False
            
        print(f"📊 找到 {len(requests_data)} 个请求")
        
        # 选择第一个请求进行测试
        test_request = requests_data[0]
        print(f"🎯 选择测试请求: {test_request['project_name']} (ID: {test_request['id']})")
        
        # 测试API
        api_success = self.test_request_details_api(test_request['id'])
        
        # 测试前端
        frontend_success = self.test_frontend_modal()
        
        # 总结结果
        print(f"\n{'='*50}")
        print("查看详情功能测试总结")
        print('='*50)
        print(f"API功能: {'✅ 通过' if api_success else '❌ 失败'}")
        print(f"前端功能: {'✅ 通过' if frontend_success else '❌ 失败'}")
        
        overall_success = api_success and frontend_success
        print(f"\n总体结果: {'🎉 全部通过' if overall_success else '❌ 存在问题'}")
        
        if overall_success:
            print("\n✨ 查看详情功能已完全实现并正常工作！")
        else:
            print("\n⚠️ 查看详情功能存在问题，需要进一步调试")
            
        return overall_success

if __name__ == "__main__":
    # 检查服务器是否运行
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = ViewRequestDetailsTest()
    success = tester.run_complete_test()
    
    sys.exit(0 if success else 1)
