#!/usr/bin/env python3
"""
详细逐步调试完成请求功能
"""

import os
import sys
import tempfile

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def debug_detailed_steps():
    """详细逐步调试"""
    print("🔍 详细逐步调试完成请求功能...")
    
    # 步骤1: 创建测试请求
    print("\n步骤1: 创建测试请求...")
    try:
        from app.models.user_request import UserRequest
        
        request_data = {
            'user_email': '<EMAIL>',
            'project_name': 'Debug Detailed Test',
            'official_website': 'https://example.com',
            'status': 'pending'
        }
        
        request_result = UserRequest.create(request_data)
        if not request_result:
            print("❌ 请求创建失败")
            return
            
        request_id = request_result.get('id')
        print(f"✅ 请求创建成功: {request_id}")
        
    except Exception as e:
        print(f"❌ 步骤1失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 步骤2: 获取管理员用户信息
    print("\n步骤2: 获取管理员用户信息...")
    try:
        from app.models.admin_user import AdminUser
        
        admin_user = AdminUser.get_by_email('<EMAIL>')
        if not admin_user:
            print("❌ 无法获取管理员用户")
            return
            
        print(f"✅ 管理员用户获取成功")
        print(f"   用户ID: {admin_user.id} (类型: {type(admin_user.id)})")
        print(f"   用户邮箱: {admin_user.email}")
        print(f"   用户名称: {admin_user.name}")
        
    except Exception as e:
        print(f"❌ 步骤2失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 步骤3: 测试直接创建报告（不通过文件上传）
    print("\n步骤3: 测试直接创建报告...")
    try:
        from app.models.research_report import ResearchReport
        
        report_data = {
            'project_name': request_result['project_name'],
            'official_website': request_result['official_website'],
            'creator_name': 'Debug Detailed Tester',
            'report_file_path': 'reports/debug_detailed_test.md',
            'analysis_file_path': 'analysis/debug_detailed_test.html',
            'description': '详细调试测试报告',
            'created_by': admin_user.id,
            'is_published': True
        }
        
        print(f"报告数据: {report_data}")
        
        created_report = ResearchReport.create(report_data)
        if created_report:
            print(f"✅ 报告创建成功: {created_report.get('id')}")
        else:
            print("❌ 报告创建失败")
            return
            
    except Exception as e:
        print(f"❌ 步骤3失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 步骤4: 测试请求状态更新
    print("\n步骤4: 测试请求状态更新...")
    try:
        success = UserRequest.update_status(request_id, 'completed', admin_user.id, '详细调试测试完成')
        if success:
            print("✅ 请求状态更新成功")
        else:
            print("❌ 请求状态更新失败")
            return
            
    except Exception as e:
        print(f"❌ 步骤4失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 步骤5: 验证完整流程
    print("\n步骤5: 验证完整流程...")
    try:
        # 验证请求状态
        updated_request = UserRequest.get_by_id(request_id)
        if updated_request and updated_request.get('status') == 'completed':
            print("✅ 请求状态验证成功")
        else:
            print(f"❌ 请求状态验证失败: {updated_request.get('status') if updated_request else 'None'}")
            
        # 验证报告存在
        if created_report:
            report_check = ResearchReport.get_by_id(created_report.get('id'))
            if report_check:
                print("✅ 报告存在验证成功")
            else:
                print("❌ 报告存在验证失败")
                
    except Exception as e:
        print(f"❌ 步骤5失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🎉 所有步骤都成功完成！")
    print("这意味着问题可能出现在HTTP请求处理或文件上传过程中。")
    
    # 步骤6: 测试文件上传相关功能
    print("\n步骤6: 测试文件上传相关功能...")
    try:
        from werkzeug.datastructures import FileStorage
        from app.utils.file_handler import allowed_file
        import io
        
        # 创建测试文件
        md_content = "# Debug File Test\n\nThis is a file upload test."
        html_content = "<html><body><h1>Debug File Test</h1></body></html>"
        
        md_file = FileStorage(
            stream=io.BytesIO(md_content.encode('utf-8')),
            filename='debug_file_test.md',
            content_type='text/markdown'
        )
        
        html_file = FileStorage(
            stream=io.BytesIO(html_content.encode('utf-8')),
            filename='debug_file_test.html',
            content_type='text/html'
        )
        
        # 测试文件验证
        print(f"MD文件验证: {allowed_file('debug_file_test.md', ['md'])}")
        print(f"HTML文件验证: {allowed_file('debug_file_test.html', ['html', 'htm'])}")
        
        print("✅ 文件验证功能正常")
        
        # 注意：不测试实际文件保存，因为需要Flask应用上下文
        print("⚠️ 文件保存需要Flask应用上下文，跳过测试")
        
    except Exception as e:
        print(f"❌ 步骤6失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_detailed_steps()
