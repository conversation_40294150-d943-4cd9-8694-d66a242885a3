#!/usr/bin/env python3
"""
核心测试运行器
只运行最关键的测试，确保部署准备就绪
"""

import os
import sys
import subprocess
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_test_module(module_name, description):
    """运行单个测试模块"""
    print(f"\n{'='*50}")
    print(f"🧪 {description}")
    print(f"{'='*50}")
    
    try:
        # 获取当前脚本所在目录
        test_dir = os.path.dirname(os.path.abspath(__file__))
        # 获取项目根目录
        project_root = os.path.dirname(test_dir)
        
        # 运行测试模块
        result = subprocess.run([
            sys.executable, 
            os.path.join(test_dir, module_name)
        ], 
        cwd=project_root,
        capture_output=True, 
        text=True
        )
        
        print(result.stdout)
        if result.stderr:
            print("⚠️ 错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - 通过")
            return True
        else:
            print(f"❌ {description} - 失败")
            return False
            
    except Exception as e:
        print(f"❌ {description} - 运行出错: {e}")
        return False

def main():
    """主函数"""
    print("🚀 项目研究报告平台 - 核心测试套件")
    print("=" * 60)
    print("运行最关键的测试，确保部署准备就绪")
    print("=" * 60)
    
    # 定义核心测试模块（按重要性排序）
    core_tests = [
        ("test_deployment.py", "部署验证测试"),
        ("test_final_verification.py", "最终验证测试"),
        ("test_supabase.py", "数据库连接测试")
    ]
    
    passed = 0
    total = len(core_tests)
    failed_tests = []
    
    for module_name, description in core_tests:
        if run_test_module(module_name, description):
            passed += 1
        else:
            failed_tests.append(description)
    
    # 输出总结
    print("\n" + "=" * 60)
    print("🏁 核心测试总结")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if failed_tests:
        print("\n❌ 失败的测试:")
        for test in failed_tests:
            print(f"  - {test}")
    
    if passed == total:
        print("\n🎉 所有核心测试通过！")
        print("\n✅ 应用已准备好部署到Vercel")
        
        print("\n📋 部署检查清单:")
        print("  ✅ 所有关键文件存在")
        print("  ✅ 模板文件完整")
        print("  ✅ 路由配置正确")
        print("  ✅ 数据库连接正常")
        print("  ✅ Vercel配置验证")
        
        print("\n🚀 部署步骤:")
        print("  1. 在Vercel中设置环境变量:")
        print("     - SUPABASE_URL")
        print("     - SUPABASE_KEY") 
        print("     - FLASK_SECRET_KEY")
        print("  2. 推送代码到Git仓库")
        print("  3. 在Vercel中连接仓库并部署")
        print("  4. 部署后访问应用验证功能")
        
        print("\n🔗 重要链接:")
        print("  - 首页: https://your-app.vercel.app")
        print("  - 管理后台: https://your-app.vercel.app/admin/login")
        print("  - 健康检查: https://your-app.vercel.app/health")
        
        return True
    else:
        print(f"\n⚠️  有 {total - passed} 个核心测试失败")
        print("请先修复这些关键问题再部署:")
        
        if "部署验证测试" in failed_tests:
            print("  🔧 检查关键文件和配置")
        if "最终验证测试" in failed_tests:
            print("  🔧 检查应用功能和模板")
        if "数据库连接测试" in failed_tests:
            print("  🔧 检查Supabase配置和连接")
            
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
