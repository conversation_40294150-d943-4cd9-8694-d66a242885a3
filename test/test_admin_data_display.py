#!/usr/bin/env python3
"""
管理员数据显示测试
测试管理员后台的数据显示问题
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_admin_dashboard_data():
    """测试管理员仪表板数据"""
    print("测试管理员仪表板数据...")
    
    try:
        from app import create_app
        from app.models.research_report import ResearchReport
        from app.models.user_request import UserRequest
        
        app = create_app()
        
        with app.app_context():
            # 测试统计数据
            total_reports = ResearchReport.get_total_count()
            pending_requests = UserRequest.get_pending_count()
            recent_reports = ResearchReport.get_recent_reports(limit=5)
            recent_requests = UserRequest.get_recent_requests(limit=5)
            
            print(f"  总报告数: {total_reports}")
            print(f"  待处理请求: {pending_requests}")
            print(f"  最近报告数: {len(recent_reports)}")
            print(f"  最近请求数: {len(recent_requests)}")
            
            # 构建统计数据
            stats = {
                'total_reports': total_reports,
                'pending_requests': pending_requests,
                'recent_reports': recent_reports,
                'recent_requests': recent_requests
            }
            
            # 测试模板渲染
            with app.test_request_context():
                from flask import render_template
                rendered = render_template('admin/dashboard.html', stats=stats)
                
                # 检查渲染结果
                if str(total_reports) in rendered:
                    print(f"✓ 仪表板模板正确显示报告总数: {total_reports}")
                else:
                    print(f"✗ 仪表板模板未显示报告总数")
                    return False
                
                if str(pending_requests) in rendered:
                    print(f"✓ 仪表板模板正确显示待处理请求: {pending_requests}")
                else:
                    print(f"✗ 仪表板模板未显示待处理请求")
                
                return True
                
    except Exception as e:
        print(f"✗ 仪表板数据测试失败: {e}")
        return False

def test_admin_reports_page():
    """测试管理员报告页面"""
    print("测试管理员报告页面...")
    
    try:
        from app import create_app
        from app.models.research_report import ResearchReport
        import math
        
        app = create_app()
        
        with app.app_context():
            # 模拟报告页面逻辑
            page = 1
            per_page = 10
            
            reports, total_count = ResearchReport.get_all_reports(page=page, per_page=per_page)
            
            print(f"  查询到的报告数: {len(reports)}")
            print(f"  总报告数: {total_count}")
            
            # 计算分页信息
            total_pages = math.ceil(total_count / per_page)
            has_prev = page > 1
            has_next = page < total_pages
            
            pagination = {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'total_pages': total_pages,
                'has_prev': has_prev,
                'has_next': has_next,
                'prev_num': page - 1 if has_prev else None,
                'next_num': page + 1 if has_next else None
            }
            
            print(f"  分页信息: {pagination}")
            
            # 测试模板渲染
            with app.test_request_context():
                from flask import render_template
                rendered = render_template('admin/reports.html', reports=reports, pagination=pagination)
                
                # 检查渲染结果
                if str(total_count) in rendered:
                    print(f"✓ 报告页面正确显示总数: {total_count}")
                else:
                    print(f"✗ 报告页面未显示总数")
                    return False
                
                if reports and reports[0].get('project_name') in rendered:
                    print(f"✓ 报告页面显示了报告内容")
                else:
                    print(f"✗ 报告页面未显示报告内容")
                    return False
                
                return True
                
    except Exception as e:
        print(f"✗ 报告页面测试失败: {e}")
        return False

def test_admin_routes_with_client():
    """使用测试客户端测试管理员路由"""
    print("测试管理员路由响应...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 测试管理员登录页面
            response = client.get('/admin/login')
            if response.status_code == 200:
                print("✓ 管理员登录页面正常")
            else:
                print(f"✗ 管理员登录页面异常: {response.status_code}")
                return False
            
            # 测试未登录访问仪表板（应该重定向）
            response = client.get('/admin/dashboard')
            if response.status_code in [302, 401]:
                print("✓ 仪表板正确保护")
            else:
                print(f"✗ 仪表板保护异常: {response.status_code}")
                return False
            
            # 测试未登录访问报告页面（应该重定向）
            response = client.get('/admin/reports')
            if response.status_code in [302, 401]:
                print("✓ 报告页面正确保护")
            else:
                print(f"✗ 报告页面保护异常: {response.status_code}")
                return False
            
            return True
            
    except Exception as e:
        print(f"✗ 路由测试失败: {e}")
        return False

def test_data_consistency():
    """测试数据一致性"""
    print("测试数据一致性...")
    
    try:
        from app.models.research_report import ResearchReport
        
        # 测试不同方法返回的数据是否一致
        published_reports, published_count = ResearchReport.get_published_reports()
        all_reports, all_count = ResearchReport.get_all_reports()
        total_count = ResearchReport.get_total_count()
        recent_reports = ResearchReport.get_recent_reports(limit=5)
        
        print(f"  已发布报告数: {published_count}")
        print(f"  所有报告数: {all_count}")
        print(f"  总报告数: {total_count}")
        print(f"  最近报告数: {len(recent_reports)}")
        
        # 检查数据一致性
        if all_count == total_count:
            print("✓ 报告总数一致")
        else:
            print(f"✗ 报告总数不一致: all_count={all_count}, total_count={total_count}")
            return False
        
        # 检查已发布报告是否都在所有报告中
        if published_count <= all_count:
            print("✓ 已发布报告数合理")
        else:
            print(f"✗ 已发布报告数异常: published_count={published_count} > all_count={all_count}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据一致性测试失败: {e}")
        return False

def run_admin_data_tests():
    """运行所有管理员数据显示测试"""
    print("=" * 60)
    print("管理员数据显示问题诊断")
    print("=" * 60)
    
    tests = [
        ("数据一致性", test_data_consistency),
        ("仪表板数据", test_admin_dashboard_data),
        ("报告页面数据", test_admin_reports_page),
        ("路由响应", test_admin_routes_with_client)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试出错: {test_name} - {e}")
    
    print("\n" + "=" * 60)
    print(f"管理员数据测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有管理员数据测试通过！")
        print("\n✅ 可能的问题原因:")
        print("  1. 浏览器缓存 - 尝试硬刷新 (Ctrl+F5)")
        print("  2. 管理员未登录 - 确保已登录管理员账户")
        print("  3. JavaScript错误 - 检查浏览器控制台")
        print("  4. CSS样式问题 - 数据可能被隐藏")
        print("\n🔧 建议解决步骤:")
        print("  1. 清除浏览器缓存")
        print("  2. 重新登录管理员账户")
        print("  3. 检查浏览器开发者工具")
        print("  4. 尝试无痕模式访问")
    else:
        print("❌ 发现管理员数据显示问题")
    
    return passed == total

if __name__ == '__main__':
    success = run_admin_data_tests()
    sys.exit(0 if success else 1)
