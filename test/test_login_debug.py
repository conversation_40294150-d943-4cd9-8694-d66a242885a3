#!/usr/bin/env python3
"""
调试管理员登录问题
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_flask_login_config():
    """测试Flask-Login配置"""
    print("🔍 测试Flask-Login配置...")
    
    try:
        from app import create_app
        from flask_login import current_user
        
        app = create_app()
        
        with app.app_context():
            with app.test_request_context():
                # 检查login_manager配置
                login_manager = app.login_manager
                print(f"✅ Login Manager: {login_manager}")
                print(f"  login_view: {login_manager.login_view}")
                print(f"  login_message: {login_manager.login_message}")
                
                # 检查user_loader
                user_loader = login_manager._user_callback
                print(f"  user_loader: {user_loader}")
                
                # 测试user_loader函数
                if user_loader:
                    print("🧪 测试user_loader函数...")
                    
                    # 使用真实的管理员ID测试
                    from app.models.admin_user import AdminUser
                    admin = AdminUser.get_by_email('<EMAIL>')
                    
                    if admin:
                        print(f"  管理员ID: {admin.id}")
                        
                        # 测试user_loader
                        loaded_user = user_loader(admin.id)
                        if loaded_user:
                            print(f"✅ user_loader成功加载用户: {loaded_user.email}")
                            print(f"  用户激活状态: {loaded_user.is_active}")
                            print(f"  用户认证状态: {loaded_user.is_authenticated}")
                        else:
                            print("❌ user_loader无法加载用户")
                    else:
                        print("❌ 无法获取管理员用户")
                
                return True
                
    except Exception as e:
        print(f"❌ Flask-Login配置测试失败: {e}")
        return False

def test_session_config():
    """测试Session配置"""
    print("\n🔍 测试Session配置...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        print(f"✅ Flask应用配置:")
        print(f"  SECRET_KEY: {'设置' if app.config.get('SECRET_KEY') else '未设置'}")
        print(f"  SESSION_COOKIE_SECURE: {app.config.get('SESSION_COOKIE_SECURE', False)}")
        print(f"  SESSION_COOKIE_HTTPONLY: {app.config.get('SESSION_COOKIE_HTTPONLY', True)}")
        print(f"  SESSION_COOKIE_SAMESITE: {app.config.get('SESSION_COOKIE_SAMESITE', 'Lax')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Session配置测试失败: {e}")
        return False

def test_login_flow():
    """测试完整登录流程"""
    print("\n🔍 测试完整登录流程...")
    
    try:
        from app import create_app
        from app.models.admin_user import AdminUser
        from flask_login import login_user, current_user
        
        app = create_app()
        
        with app.app_context():
            with app.test_request_context():
                # 步骤1: 获取用户
                print("步骤1: 获取管理员用户...")
                user = AdminUser.get_by_email('<EMAIL>')
                if not user:
                    print("❌ 无法获取用户")
                    return False
                print(f"✅ 用户获取成功: {user.email}")
                
                # 步骤2: 验证密码
                print("步骤2: 验证密码...")
                if not user.check_password('admin123'):
                    print("❌ 密码验证失败")
                    return False
                print("✅ 密码验证成功")
                
                # 步骤3: 检查用户状态
                print("步骤3: 检查用户状态...")
                print(f"  is_active: {user.is_active}")
                print(f"  is_authenticated: {user.is_authenticated}")
                print(f"  is_anonymous: {user.is_anonymous}")
                
                # 步骤4: 尝试登录
                print("步骤4: 尝试登录...")
                login_result = login_user(user)
                print(f"  login_user结果: {login_result}")
                
                # 步骤5: 检查当前用户状态
                print("步骤5: 检查当前用户状态...")
                print(f"  current_user.is_authenticated: {current_user.is_authenticated}")
                print(f"  current_user.id: {getattr(current_user, 'id', 'None')}")
                
                return login_result
                
    except Exception as e:
        print(f"❌ 登录流程测试失败: {e}")
        return False

def test_admin_user_methods():
    """测试AdminUser模型的方法"""
    print("\n🔍 测试AdminUser模型方法...")
    
    try:
        from app.models.admin_user import AdminUser
        
        # 测试get_by_email
        print("测试get_by_email...")
        user = AdminUser.get_by_email('<EMAIL>')
        if user:
            print(f"✅ get_by_email成功: {user.email}")
            
            # 测试get_by_id
            print("测试get_by_id...")
            user_by_id = AdminUser.get_by_id(user.id)
            if user_by_id:
                print(f"✅ get_by_id成功: {user_by_id.email}")
            else:
                print("❌ get_by_id失败")
                return False
                
            # 测试UserMixin方法
            print("测试UserMixin方法...")
            print(f"  get_id(): {user.get_id()}")
            print(f"  is_active: {user.is_active}")
            print(f"  is_authenticated: {user.is_authenticated}")
            print(f"  is_anonymous: {user.is_anonymous}")
            
            return True
        else:
            print("❌ get_by_email失败")
            return False
            
    except Exception as e:
        print(f"❌ AdminUser模型测试失败: {e}")
        return False

def test_with_test_client():
    """使用Flask测试客户端测试登录"""
    print("\n🔍 使用Flask测试客户端测试登录...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 获取登录页面
            print("获取登录页面...")
            response = client.get('/admin/login')
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 提取CSRF token
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.data, 'html.parser')
                csrf_token = soup.find('input', {'name': 'csrf_token'})
                csrf_value = csrf_token['value'] if csrf_token else None
                print(f"  CSRF token: {'找到' if csrf_value else '未找到'}")
                
                # 提交登录表单
                print("提交登录表单...")
                login_data = {
                    'email': '<EMAIL>',
                    'password': 'admin123'
                }
                if csrf_value:
                    login_data['csrf_token'] = csrf_value
                
                response = client.post('/admin/login', data=login_data, follow_redirects=False)
                print(f"  登录响应状态码: {response.status_code}")
                print(f"  Location header: {response.headers.get('Location', 'None')}")
                
                if response.status_code == 302:
                    # 跟踪重定向
                    location = response.headers.get('Location', '')
                    print(f"  重定向到: {location}")
                    
                    if 'dashboard' in location:
                        print("✅ 登录成功，重定向到dashboard")
                        return True
                    elif 'login' in location:
                        print("❌ 登录失败，重定向回登录页面")
                        return False
                    else:
                        print(f"⚠️ 重定向到未知页面: {location}")
                        return False
                else:
                    print(f"❌ 登录失败，状态码: {response.status_code}")
                    return False
            else:
                print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Flask测试客户端测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始管理员登录调试...")
    
    # 运行各项测试
    tests = [
        ("Flask-Login配置", test_flask_login_config),
        ("Session配置", test_session_config),
        ("AdminUser模型方法", test_admin_user_methods),
        ("完整登录流程", test_login_flow),
        ("Flask测试客户端", test_with_test_client)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n结果: {'✅ 通过' if result else '❌ 失败'}")
        except Exception as e:
            print(f"\n结果: ❌ 异常 - {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*50}")
    print("测试总结")
    print('='*50)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 通过")

if __name__ == "__main__":
    main()
