#!/usr/bin/env python3
"""
验证用户请求状态更新是否真的生效
"""

import os
import sys
import time
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class RequestStatusVerificationTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def login_admin(self):
        """登录管理员账户"""
        print("🔐 登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        
        # 登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': self.csrf_token
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if 'dashboard' in response.url.lower():
            print("✅ 管理员登录成功")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
            
    def get_request_status(self, request_id):
        """获取指定请求的当前状态"""
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            return None
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找包含指定request_id的行
        for row in soup.find_all('tr'):
            buttons = row.find_all('button')
            for button in buttons:
                onclick = button.get('onclick', '')
                if request_id in onclick:
                    # 找到对应的行，获取状态
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        status_cell = cells[2]
                        return status_cell.get_text().strip()
        
        return None
        
    def update_request_status(self, request_id, new_status):
        """更新请求状态"""
        # 更新CSRF token
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        self.csrf_token = self.get_csrf_token(requests_url)
        
        # 发送状态更新请求
        status_url = urljoin(self.base_url, f'/admin/requests/{request_id}/status')
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': self.csrf_token or ''
        }
        
        data = {
            'status': new_status
        }
        
        response = self.session.post(status_url, headers=headers, json=data)
        
        if response.status_code == 200:
            try:
                result = response.json()
                return result.get('success', False)
            except:
                return False
        
        return False
        
    def verify_database_update(self, request_id):
        """直接从数据库验证状态更新"""
        try:
            from app.models.user_request import UserRequest
            
            request_data = UserRequest.get_by_id(request_id)
            if request_data:
                return request_data.get('status')
            return None
            
        except Exception as e:
            print(f"数据库验证失败: {e}")
            return None
            
    def run_verification_test(self):
        """运行完整的验证测试"""
        print("🚀 开始用户请求状态更新验证测试...")
        
        # 登录
        if not self.login_admin():
            return False
            
        # 找一个待处理的请求
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        test_request_id = None
        for row in soup.find_all('tr'):
            cells = row.find_all('td')
            if len(cells) >= 3:
                status = cells[2].get_text().strip()
                if '待处理' in status:
                    # 找到这行的request_id
                    buttons = row.find_all('button')
                    for button in buttons:
                        onclick = button.get('onclick', '')
                        if 'viewRequest' in onclick:
                            import re
                            match = re.search(r"viewRequest\('([^']+)'\)", onclick)
                            if match:
                                test_request_id = match.group(1)
                                break
                    if test_request_id:
                        break
        
        if not test_request_id:
            print("❌ 没有找到待处理的请求")
            return False
            
        print(f"🎯 选择测试请求ID: {test_request_id}")
        
        # 步骤1: 获取初始状态
        initial_status_ui = self.get_request_status(test_request_id)
        initial_status_db = self.verify_database_update(test_request_id)
        
        print(f"初始状态 (UI): {initial_status_ui}")
        print(f"初始状态 (DB): {initial_status_db}")
        
        # 步骤2: 更新状态为"处理中"
        print("\n🔄 更新状态为'处理中'...")
        update_success = self.update_request_status(test_request_id, 'processing')
        print(f"API响应: {'✅ 成功' if update_success else '❌ 失败'}")
        
        if not update_success:
            print("❌ API更新失败，停止测试")
            return False
            
        # 等待一秒确保更新完成
        time.sleep(1)
        
        # 步骤3: 验证状态是否真的更新了
        print("\n🔍 验证状态更新...")
        
        # 检查UI显示
        updated_status_ui = self.get_request_status(test_request_id)
        print(f"更新后状态 (UI): {updated_status_ui}")
        
        # 检查数据库
        updated_status_db = self.verify_database_update(test_request_id)
        print(f"更新后状态 (DB): {updated_status_db}")
        
        # 验证结果
        ui_updated = updated_status_ui != initial_status_ui and '处理中' in (updated_status_ui or '')
        db_updated = updated_status_db != initial_status_db and updated_status_db == 'processing'
        
        print(f"\n📊 验证结果:")
        print(f"UI状态更新: {'✅ 成功' if ui_updated else '❌ 失败'}")
        print(f"数据库状态更新: {'✅ 成功' if db_updated else '❌ 失败'}")
        
        if ui_updated and db_updated:
            print("\n🎉 状态更新功能完全正常！")
            return True
        elif not ui_updated and not db_updated:
            print("\n❌ 状态更新功能完全失败！")
            print("问题可能是:")
            print("1. 数据库权限问题")
            print("2. API逻辑错误")
            print("3. 前端JavaScript问题")
            return False
        elif db_updated and not ui_updated:
            print("\n⚠️ 数据库更新成功，但UI显示未更新")
            print("问题可能是:")
            print("1. 页面缓存问题")
            print("2. 前端刷新逻辑问题")
            return False
        else:
            print("\n⚠️ UI显示更新，但数据库未更新")
            print("这种情况不太可能发生，可能是测试逻辑问题")
            return False

if __name__ == "__main__":
    # 检查服务器是否运行
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = RequestStatusVerificationTest()
    success = tester.run_verification_test()
    
    sys.exit(0 if success else 1)
