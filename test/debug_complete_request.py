#!/usr/bin/env python3
"""
调试完成请求功能
"""

import os
import sys
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup
import tempfile

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def debug_complete_request():
    """调试完成请求功能"""
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # 登录管理员
    print("🔐 登录管理员...")
    login_url = urljoin(base_url, '/admin/login')
    response = session.get(login_url)
    
    soup = BeautifulSoup(response.text, 'html.parser')
    csrf_meta = soup.find('meta', {'name': 'csrf-token'})
    csrf_token = csrf_meta.get('content') if csrf_meta else None
    
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123',
        'csrf_token': csrf_token
    }
    
    response = session.post(login_url, data=login_data, allow_redirects=True)
    
    if 'dashboard' not in response.url.lower():
        print("❌ 登录失败")
        return
        
    print("✅ 登录成功")
    
    # 创建测试请求
    print("\n📝 创建测试请求...")
    try:
        from app.models.user_request import UserRequest
        
        request_data = {
            'user_email': '<EMAIL>',
            'project_name': 'Debug Test',
            'official_website': 'https://example.com',
            'status': 'pending'
        }
        
        result = UserRequest.create(request_data)
        
        if not result:
            print("❌ 创建测试请求失败")
            return
            
        request_id = result.get('id')
        print(f"✅ 测试请求创建成功，ID: {request_id}")
        
    except Exception as e:
        print(f"❌ 创建测试请求时出错: {e}")
        return
    
    # 测试不同的API调用
    print(f"\n🔍 测试API调用...")
    
    # 1. 测试获取请求详情
    print("1. 测试获取请求详情...")
    details_url = urljoin(base_url, f'/admin/requests/{request_id}')
    response = session.get(details_url)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"   响应: {data.get('success', False)}")
        except:
            print("   响应不是JSON")
    
    # 2. 测试简单的状态更新
    print("2. 测试状态更新...")
    status_url = urljoin(base_url, f'/admin/requests/{request_id}/status')
    
    # 更新CSRF token
    requests_url = urljoin(base_url, '/admin/requests')
    response = session.get(requests_url)
    soup = BeautifulSoup(response.text, 'html.parser')
    csrf_meta = soup.find('meta', {'name': 'csrf-token'})
    csrf_token = csrf_meta.get('content') if csrf_meta else None
    
    headers = {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrf_token or ''
    }
    
    data = {'status': 'processing'}
    response = session.post(status_url, headers=headers, json=data)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        try:
            result = response.json()
            print(f"   响应: {result.get('success', False)}")
        except:
            print("   响应不是JSON")
    
    # 3. 测试完成请求API（不带文件）
    print("3. 测试完成请求API（不带文件）...")
    complete_url = urljoin(base_url, f'/admin/requests/{request_id}/complete')
    
    data = {
        'creator_name': 'Debug Test Admin',
        'description': '调试测试',
        'admin_notes': '调试'
    }
    
    headers = {
        'X-CSRFToken': csrf_token or ''
    }
    
    response = session.post(complete_url, data=data, headers=headers)
    print(f"   状态码: {response.status_code}")
    print(f"   响应内容: {response.text[:200]}...")
    
    # 4. 测试带文件的完成请求API
    print("4. 测试完成请求API（带文件）...")
    
    # 创建测试文件
    md_content = "# Debug Test Report\n\nThis is a debug test."
    html_content = "<html><body><h1>Debug Test</h1></body></html>"
    
    md_file = tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8')
    md_file.write(md_content)
    md_file.close()
    
    html_file = tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8')
    html_file.write(html_content)
    html_file.close()
    
    try:
        files = {
            'report_file': ('debug_report.md', open(md_file.name, 'rb'), 'text/markdown'),
            'analysis_file': ('debug_analysis.html', open(html_file.name, 'rb'), 'text/html')
        }
        
        data = {
            'creator_name': 'Debug Test Admin',
            'description': '调试测试带文件',
            'admin_notes': '调试带文件'
        }
        
        headers = {
            'X-CSRFToken': csrf_token or ''
        }
        
        response = session.post(complete_url, files=files, data=data, headers=headers)
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text[:500]}...")
        
        if response.status_code == 500:
            print("\n🔍 500错误详情分析:")
            print("可能的原因:")
            print("1. 文件上传处理错误")
            print("2. 数据库操作错误")
            print("3. 文件存储服务错误")
            print("4. 模型方法调用错误")
            
    finally:
        # 清理临时文件
        try:
            os.unlink(md_file.name)
            os.unlink(html_file.name)
        except:
            pass

if __name__ == "__main__":
    debug_complete_request()
