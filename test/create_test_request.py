#!/usr/bin/env python3
"""
创建测试请求用于测试拒绝功能
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_test_request():
    """直接在数据库中创建测试请求"""
    try:
        from app.models.user_request import UserRequest
        
        request_data = {
            'user_email': '<EMAIL>',
            'project_name': 'React',
            'official_website': 'https://reactjs.org',
            'status': 'pending'
        }
        
        result = UserRequest.create(request_data)
        
        if result:
            print(f"✅ 测试请求创建成功")
            print(f"请求ID: {result.get('id')}")
            print(f"项目名称: {result.get('project_name')}")
            print(f"用户邮箱: {result.get('user_email')}")
            print(f"状态: {result.get('status')}")
            return True
        else:
            print("❌ 测试请求创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建测试请求时出错: {e}")
        return False

if __name__ == "__main__":
    print("🚀 创建测试请求...")
    success = create_test_request()
    sys.exit(0 if success else 1)
