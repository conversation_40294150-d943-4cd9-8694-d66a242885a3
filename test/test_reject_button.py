#!/usr/bin/env python3
"""
专门测试拒绝按钮功能
"""

import os
import sys
import time
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class RejectButtonTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def create_test_request(self):
        """创建一个测试请求"""
        print("📝 创建测试请求...")

        # 访问首页获取CSRF token
        home_url = urljoin(self.base_url, '/')
        response = self.session.get(home_url)

        if response.status_code != 200:
            print("❌ 无法访问首页")
            return False

        self.csrf_token = self.get_csrf_token(home_url)

        # 使用AJAX API提交请求
        request_url = urljoin(self.base_url, '/request-project')

        request_data = {
            'project_name': 'Vue.js',
            'email': '<EMAIL>',
            'official_website': 'https://vuejs.org'
        }

        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': self.csrf_token or ''
        }

        print(f"发送请求到: {request_url}")
        print(f"请求数据: {request_data}")
        print(f"CSRF Token: {self.csrf_token[:20]}..." if self.csrf_token else "无CSRF Token")

        response = self.session.post(request_url, headers=headers, json=request_data)

        print(f"请求提交响应状态码: {response.status_code}")
        print(f"响应内容: {response.text[:300]}...")

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print("✅ 测试请求创建成功")
                    return True
                else:
                    print(f"❌ 测试请求创建失败: {result.get('errors', '未知错误')}")
                    return False
            except:
                print("❌ 响应不是JSON格式")
                return False
        else:
            print(f"❌ 测试请求创建失败，状态码: {response.status_code}")
            return False
            
    def login_admin(self):
        """登录管理员账户"""
        print("🔐 登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        
        # 登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': self.csrf_token
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if 'dashboard' in response.url.lower():
            print("✅ 管理员登录成功")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
            
    def find_pending_request(self):
        """查找待处理的请求"""
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            return None
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找待处理的请求
        for row in soup.find_all('tr')[1:]:  # 跳过表头
            cells = row.find_all('td')
            if len(cells) >= 3:
                status = cells[2].get_text().strip()
                if '待处理' in status:
                    # 提取request_id
                    buttons = row.find_all('button')
                    for button in buttons:
                        onclick = button.get('onclick', '')
                        if 'viewRequest' in onclick:
                            import re
                            match = re.search(r"viewRequest\('([^']+)'\)", onclick)
                            if match:
                                project_name = cells[0].get_text().strip()
                                return {
                                    'id': match.group(1),
                                    'project_name': project_name,
                                    'status': status
                                }
        
        return None
        
    def test_reject_button(self, request_id, project_name):
        """测试拒绝按钮"""
        print(f"\n❌ 测试拒绝按钮功能")
        print(f"请求: {project_name} (ID: {request_id})")
        
        # 更新CSRF token
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        self.csrf_token = self.get_csrf_token(requests_url)
        
        # 发送拒绝请求
        status_url = urljoin(self.base_url, f'/admin/requests/{request_id}/status')
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': self.csrf_token or ''
        }
        
        data = {
            'status': 'rejected',
            'admin_notes': '测试拒绝功能'
        }
        
        print(f"发送拒绝请求到: {status_url}")
        
        response = self.session.post(status_url, headers=headers, json=data)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"响应数据: {result}")
                
                if result.get('success'):
                    print("✅ 拒绝操作API调用成功")
                    
                    # 验证状态是否真的更新了
                    time.sleep(1)
                    updated_request = self.find_request_by_id(request_id)
                    
                    if updated_request and '已拒绝' in updated_request.get('status', ''):
                        print("✅ 请求状态已成功更新为'已拒绝'")
                        return True
                    else:
                        print("❌ 请求状态未正确更新")
                        return False
                else:
                    print(f"❌ 拒绝操作失败: {result.get('message', '未知错误')}")
                    return False
            except:
                print("❌ 响应不是JSON格式")
                return False
        else:
            print(f"❌ 拒绝请求失败，状态码: {response.status_code}")
            return False
            
    def find_request_by_id(self, request_id):
        """根据ID查找请求"""
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            return None
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        for row in soup.find_all('tr')[1:]:  # 跳过表头
            buttons = row.find_all('button')
            for button in buttons:
                onclick = button.get('onclick', '')
                if request_id in onclick:
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        return {
                            'project_name': cells[0].get_text().strip(),
                            'status': cells[2].get_text().strip()
                        }
        
        return None
        
    def run_reject_test(self):
        """运行拒绝按钮测试"""
        print("🚀 开始拒绝按钮功能测试...")
        print("="*50)

        # 登录管理员
        if not self.login_admin():
            return False

        # 查找待处理的请求
        pending_request = self.find_pending_request()

        if not pending_request:
            print("❌ 没有找到待处理的请求")
            print("⚠️ 请先创建一些待处理的请求来测试拒绝功能")
            return False

        print(f"🎯 找到待处理请求: {pending_request['project_name']}")

        # 测试拒绝功能
        success = self.test_reject_button(
            pending_request['id'],
            pending_request['project_name']
        )

        # 生成测试报告
        print(f"\n{'='*50}")
        print("拒绝按钮功能测试报告")
        print('='*50)

        if success:
            print("🎉 拒绝按钮功能测试通过！")
            print("✅ 功能正常:")
            print("   • API调用成功")
            print("   • 状态正确更新")
            print("   • 数据库持久化正常")
        else:
            print("❌ 拒绝按钮功能测试失败！")
            print("需要检查:")
            print("   • API路由是否正确")
            print("   • 数据库更新逻辑")
            print("   • 前端JavaScript代码")

        return success

if __name__ == "__main__":
    # 检查服务器是否运行
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = RejectButtonTest()
    success = tester.run_reject_test()
    
    sys.exit(0 if success else 1)
