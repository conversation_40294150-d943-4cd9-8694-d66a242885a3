#!/usr/bin/env python3
"""
测试拒绝请求删除功能
"""

import os
import sys
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class RejectRequestDeletionTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def login_admin(self):
        """登录管理员账户"""
        print("🔐 登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        
        # 登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': self.csrf_token
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if 'dashboard' in response.url.lower():
            print("✅ 管理员登录成功")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
            
    def create_test_request(self):
        """创建测试请求"""
        print("\n📝 创建测试请求...")
        try:
            from app.models.user_request import UserRequest
            
            request_data = {
                'user_email': '<EMAIL>',
                'project_name': 'Reject Test Project',
                'official_website': 'https://reject-test.com',
                'status': 'pending'
            }
            
            result = UserRequest.create(request_data)
            
            if result:
                request_id = result.get('id')
                print(f"✅ 测试请求创建成功，ID: {request_id}")
                return request_id
            else:
                print("❌ 测试请求创建失败")
                return None
                
        except Exception as e:
            print(f"❌ 创建测试请求时出错: {e}")
            return None
            
    def get_request_count_before(self):
        """获取拒绝前的请求数量"""
        print("\n📊 获取拒绝前的请求数量...")
        try:
            from app.models.user_request import UserRequest
            
            all_requests, total_count = UserRequest.get_all_requests(page=1, per_page=100)
            print(f"拒绝前总请求数量: {total_count}")
            return total_count
            
        except Exception as e:
            print(f"❌ 获取请求数量失败: {e}")
            return 0
            
    def test_reject_request_api(self, request_id):
        """测试拒绝请求API"""
        print(f"\n🚫 测试拒绝请求API (ID: {request_id})")
        
        # 更新CSRF token
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        self.csrf_token = self.get_csrf_token(requests_url)
        
        # 发送拒绝请求
        status_url = urljoin(self.base_url, f'/admin/requests/{request_id}/status')
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': self.csrf_token or ''
        }
        
        data = {'status': 'rejected'}
        
        print(f"发送拒绝请求到: {status_url}")
        print(f"CSRF Token: {self.csrf_token[:20]}..." if self.csrf_token else "无CSRF Token")
        
        response = self.session.post(status_url, headers=headers, json=data)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"响应数据: {result}")
                
                if result.get('success'):
                    print("✅ 拒绝请求API调用成功")
                    message = result.get('message', '')
                    if '删除' in message:
                        print("✅ 响应消息确认请求已删除")
                    return True
                else:
                    print(f"❌ 拒绝请求失败: {result.get('error', '未知错误')}")
                    return False
            except:
                print("❌ 响应不是JSON格式")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ 拒绝请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    def verify_request_deleted(self, request_id):
        """验证请求是否已被删除"""
        print(f"\n🔍 验证请求是否已删除 (ID: {request_id})")
        
        try:
            from app.models.user_request import UserRequest
            
            request_data = UserRequest.get_by_id(request_id)
            if request_data is None:
                print("✅ 请求已从数据库中删除")
                return True
            else:
                print(f"❌ 请求仍然存在于数据库中: {request_data.get('status')}")
                return False
                
        except Exception as e:
            print(f"❌ 验证请求删除状态时出错: {e}")
            return False
            
    def verify_request_count_decreased(self, original_count):
        """验证请求总数是否减少"""
        print(f"\n📉 验证请求总数是否减少...")
        try:
            from app.models.user_request import UserRequest
            
            all_requests, new_total_count = UserRequest.get_all_requests(page=1, per_page=100)
            print(f"拒绝后总请求数量: {new_total_count}")
            print(f"原始数量: {original_count}")
            
            if new_total_count == original_count - 1:
                print("✅ 请求总数正确减少了1")
                return True
            else:
                print(f"❌ 请求总数变化不正确，期望减少1，实际变化: {new_total_count - original_count}")
                return False
                
        except Exception as e:
            print(f"❌ 验证请求总数时出错: {e}")
            return False
            
    def test_page_display(self):
        """测试页面显示是否正确"""
        print(f"\n🖥️ 测试页面显示...")
        
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code == 200:
            # 检查页面是否包含拒绝确认消息
            if '确定要拒绝此请求吗' in response.text and '无法恢复' in response.text:
                print("✅ 页面包含正确的拒绝确认消息")
                return True
            else:
                print("❌ 页面缺少拒绝确认消息")
                return False
        else:
            print(f"❌ 无法访问请求管理页面，状态码: {response.status_code}")
            return False
            
    def run_rejection_test(self):
        """运行拒绝请求删除测试"""
        print("🚀 开始拒绝请求删除功能测试...")
        print("="*60)
        
        # 登录管理员
        if not self.login_admin():
            return False
            
        # 获取拒绝前的请求数量
        original_count = self.get_request_count_before()
        
        # 创建测试请求
        request_id = self.create_test_request()
        if not request_id:
            return False
            
        # 更新请求数量（因为新增了一个）
        original_count += 1
        
        # 测试拒绝请求API
        api_success = self.test_reject_request_api(request_id)
        
        if not api_success:
            print("❌ API测试失败，停止后续测试")
            return False
            
        # 验证请求是否被删除
        deletion_success = self.verify_request_deleted(request_id)
        
        # 验证请求总数是否减少
        count_success = self.verify_request_count_decreased(original_count)
        
        # 测试页面显示
        page_success = self.test_page_display()
        
        # 生成测试报告
        print(f"\n{'='*60}")
        print("拒绝请求删除功能测试报告")
        print('='*60)
        
        results = [
            ("API调用", api_success),
            ("请求删除", deletion_success),
            ("数量统计", count_success),
            ("页面显示", page_success)
        ]
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        overall_success = all(result for _, result in results)
        
        if overall_success:
            print(f"\n🎉 拒绝请求删除功能测试全部通过！")
            print("✨ 功能正常:")
            print("   • 拒绝请求时从数据库中删除")
            print("   • 页面统计数量正确更新")
            print("   • 用户界面显示正确的警告信息")
            print("   • API响应消息准确")
        else:
            print(f"\n❌ 拒绝请求删除功能存在问题")
            
        return overall_success

if __name__ == "__main__":
    # 检查服务器是否运行
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = RejectRequestDeletionTest()
    success = tester.run_rejection_test()
    
    sys.exit(0 if success else 1)
