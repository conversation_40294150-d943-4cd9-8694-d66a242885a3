#!/usr/bin/env python3
"""
测试完成请求的前端功能
"""

import os
import sys
import requests
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class CompleteRequestFrontendTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def login_admin(self):
        """登录管理员账户"""
        print("🔐 登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        
        # 登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': self.csrf_token
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if 'dashboard' in response.url.lower():
            print("✅ 管理员登录成功")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
            
    def test_requests_page_frontend(self):
        """测试请求管理页面的前端功能"""
        print("\n🖥️ 测试请求管理页面前端功能...")
        
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            print("❌ 无法访问请求管理页面")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 检查完成请求模态框是否存在
        complete_modal = soup.find('div', {'id': 'completeRequestModal'})
        if complete_modal:
            print("✅ 找到完成请求模态框")
        else:
            print("❌ 未找到完成请求模态框")
            return False
            
        # 检查模态框内容
        modal_title = complete_modal.find('h5', class_='modal-title')
        if modal_title and '上传报告文件' in modal_title.get_text():
            print("✅ 模态框标题正确")
        else:
            print("❌ 模态框标题不正确")
            return False
            
        # 检查文件上传字段
        report_file_input = complete_modal.find('input', {'id': 'reportFile'})
        analysis_file_input = complete_modal.find('input', {'id': 'analysisFile'})
        
        if report_file_input and analysis_file_input:
            print("✅ 找到文件上传字段")
        else:
            print("❌ 文件上传字段缺失")
            return False
            
        # 检查表单字段
        creator_name_input = complete_modal.find('input', {'id': 'creatorName'})
        description_textarea = complete_modal.find('textarea', {'id': 'reportDescription'})
        admin_notes_textarea = complete_modal.find('textarea', {'id': 'adminNotes'})
        
        if creator_name_input and description_textarea and admin_notes_textarea:
            print("✅ 找到所有表单字段")
        else:
            print("❌ 表单字段缺失")
            return False
            
        # 检查JavaScript函数
        scripts = soup.find_all('script')
        show_complete_modal_found = False
        form_submit_handler_found = False
        
        for script in scripts:
            if script.string:
                if 'showCompleteRequestModal' in script.string:
                    show_complete_modal_found = True
                if 'completeRequestForm' in script.string and 'addEventListener' in script.string:
                    form_submit_handler_found = True
                    
        if show_complete_modal_found:
            print("✅ 找到showCompleteRequestModal函数")
        else:
            print("❌ 未找到showCompleteRequestModal函数")
            return False
            
        if form_submit_handler_found:
            print("✅ 找到表单提交处理器")
        else:
            print("❌ 未找到表单提交处理器")
            return False
            
        # 检查完成按钮的onclick事件
        complete_buttons = soup.find_all('button', {'onclick': lambda x: x and 'completed' in x})
        if complete_buttons:
            print("✅ 找到完成按钮")
            
            # 检查onclick事件是否调用updateStatus
            onclick_content = complete_buttons[0].get('onclick', '')
            if 'updateStatus' in onclick_content and 'completed' in onclick_content:
                print("✅ 完成按钮onclick事件正确")
            else:
                print("❌ 完成按钮onclick事件不正确")
                return False
        else:
            print("❌ 未找到完成按钮")
            return False
            
        return True
        
    def test_complete_button_behavior(self):
        """测试完成按钮的行为"""
        print("\n🔘 测试完成按钮行为...")
        
        # 创建一个测试请求
        try:
            from app.models.user_request import UserRequest
            
            request_data = {
                'user_email': '<EMAIL>',
                'project_name': 'Frontend Test',
                'official_website': 'https://example.com',
                'status': 'pending'
            }
            
            result = UserRequest.create(request_data)
            
            if not result:
                print("❌ 创建测试请求失败")
                return False
                
            request_id = result.get('id')
            print(f"✅ 测试请求创建成功，ID: {request_id}")
            
        except Exception as e:
            print(f"❌ 创建测试请求时出错: {e}")
            return False
        
        # 检查请求是否出现在页面上
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if 'Frontend Test' in response.text:
            print("✅ 测试请求出现在页面上")
        else:
            print("❌ 测试请求未出现在页面上")
            return False
            
        # 检查完成按钮是否存在
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找包含测试请求的行
        test_request_row = None
        for row in soup.find_all('tr'):
            if 'Frontend Test' in row.get_text():
                test_request_row = row
                break
                
        if not test_request_row:
            print("❌ 未找到测试请求行")
            return False
            
        # 检查该行是否有完成按钮
        complete_button = test_request_row.find('button', {'onclick': lambda x: x and 'completed' in x})
        if complete_button:
            print("✅ 测试请求行有完成按钮")
            
            # 检查按钮是否包含正确的request_id
            onclick_content = complete_button.get('onclick', '')
            if request_id in onclick_content:
                print("✅ 完成按钮包含正确的请求ID")
            else:
                print("❌ 完成按钮不包含正确的请求ID")
                return False
        else:
            print("❌ 测试请求行没有完成按钮")
            return False
            
        return True
        
    def run_frontend_test(self):
        """运行前端功能测试"""
        print("🚀 开始完成请求前端功能测试...")
        print("="*60)
        
        # 登录
        if not self.login_admin():
            return False
            
        # 测试页面前端功能
        frontend_success = self.test_requests_page_frontend()
        
        # 测试按钮行为
        button_success = self.test_complete_button_behavior()
        
        # 生成测试报告
        print(f"\n{'='*60}")
        print("完成请求前端功能测试报告")
        print('='*60)
        
        results = [
            ("页面前端功能", frontend_success),
            ("按钮行为", button_success)
        ]
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        overall_success = all(result for _, result in results)
        
        if overall_success:
            print(f"\n🎉 完成请求前端功能测试全部通过！")
            print("✨ 前端功能正常:")
            print("   • 完成请求模态框正确显示")
            print("   • 文件上传字段存在")
            print("   • 表单字段完整")
            print("   • JavaScript函数正确实现")
            print("   • 完成按钮行为正确")
        else:
            print(f"\n❌ 完成请求前端功能存在问题")
            
        return overall_success

if __name__ == "__main__":
    # 检查服务器是否运行
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = CompleteRequestFrontendTest()
    success = tester.run_frontend_test()
    
    sys.exit(0 if success else 1)
