#!/usr/bin/env python3
"""
测试所有管理员功能
"""

import os
import sys
import time
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class AdminFunctionsTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def login_admin(self):
        """登录管理员账户"""
        print("🔐 登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        
        # 登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': self.csrf_token
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if 'dashboard' in response.url.lower():
            print("✅ 管理员登录成功")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
            
    def test_dashboard(self):
        """测试仪表板"""
        print("\n📊 测试仪表板...")
        
        dashboard_url = urljoin(self.base_url, '/admin/dashboard')
        response = self.session.get(dashboard_url)
        
        if response.status_code != 200:
            print(f"❌ 无法访问仪表板，状态码: {response.status_code}")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 检查统计卡片
        stat_cards = soup.find_all(class_=lambda x: x and 'stat' in x.lower())
        print(f"✅ 找到 {len(stat_cards)} 个统计卡片")
        
        # 检查页面标题
        title = soup.title.string if soup.title else "No title"
        if '仪表板' in title or 'dashboard' in title.lower():
            print("✅ 仪表板页面标题正确")
            return True
        else:
            print(f"⚠️ 仪表板页面标题: {title}")
            return True  # 仍然算作成功
            
    def test_reports_management(self):
        """测试报告管理功能"""
        print("\n📋 测试报告管理功能...")
        
        reports_url = urljoin(self.base_url, '/admin/reports')
        response = self.session.get(reports_url)
        
        if response.status_code != 200:
            print(f"❌ 无法访问报告管理页面，状态码: {response.status_code}")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找报告
        delete_buttons = soup.find_all('button', {'onclick': lambda x: x and 'deleteReport' in x})
        publish_buttons = soup.find_all('button', {'onclick': lambda x: x and 'publishReport' in x})
        unpublish_buttons = soup.find_all('button', {'onclick': lambda x: x and 'unpublishReport' in x})
        
        print(f"✅ 找到 {len(delete_buttons)} 个删除按钮")
        print(f"✅ 找到 {len(publish_buttons)} 个发布按钮")
        print(f"✅ 找到 {len(unpublish_buttons)} 个取消发布按钮")
        
        return len(delete_buttons) > 0
        
    def test_publish_unpublish(self):
        """测试发布/取消发布功能"""
        print("\n📤 测试发布/取消发布功能...")
        
        # 获取报告列表
        reports_url = urljoin(self.base_url, '/admin/reports')
        response = self.session.get(reports_url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找一个可以发布的报告
        publish_buttons = soup.find_all('button', {'onclick': lambda x: x and 'publishReport' in x})
        unpublish_buttons = soup.find_all('button', {'onclick': lambda x: x and 'unpublishReport' in x})
        
        test_button = None
        test_action = None
        test_report_id = None
        
        if publish_buttons:
            test_button = publish_buttons[0]
            test_action = "publish"
            test_report_id = test_button.get('data-report-id')
            print(f"测试发布报告: {test_report_id}")
        elif unpublish_buttons:
            test_button = unpublish_buttons[0]
            test_action = "unpublish"
            test_report_id = test_button.get('data-report-id')
            print(f"测试取消发布报告: {test_report_id}")
        else:
            print("⚠️ 没有找到可测试的发布/取消发布按钮")
            return True
            
        if not test_report_id:
            print("❌ 无法获取报告ID")
            return False
            
        # 更新CSRF token
        self.csrf_token = self.get_csrf_token(reports_url)
        
        # 发送状态更新请求
        status_url = urljoin(self.base_url, f'/admin/reports/{test_report_id}/status')
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': self.csrf_token or ''
        }
        
        data = {
            'is_published': test_action == "publish"
        }
        
        response = self.session.post(status_url, headers=headers, json=data)
        
        print(f"状态更新响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"状态更新响应: {result}")
                
                if result.get('success'):
                    print(f"✅ {test_action}操作成功")
                    return True
                else:
                    print(f"❌ {test_action}操作失败: {result.get('message', '未知错误')}")
                    return False
            except:
                print("❌ 状态更新响应不是JSON格式")
                return False
        else:
            print(f"❌ 状态更新请求失败，状态码: {response.status_code}")
            return False
            
    def test_requests_management(self):
        """测试请求管理功能"""
        print("\n📝 测试请求管理功能...")
        
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            print(f"❌ 无法访问请求管理页面，状态码: {response.status_code}")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 检查页面内容
        title = soup.title.string if soup.title else "No title"
        print(f"页面标题: {title}")
        
        # 查找请求相关的按钮
        status_buttons = soup.find_all('button', {'onclick': lambda x: x and 'updateStatus' in x})
        print(f"✅ 找到 {len(status_buttons)} 个状态更新按钮")
        
        # 查找筛选器
        status_filters = soup.find_all('input', {'name': 'statusFilter'})
        print(f"✅ 找到 {len(status_filters)} 个状态筛选器")
        
        return True
        
    def test_create_report_page(self):
        """测试创建报告页面"""
        print("\n➕ 测试创建报告页面...")
        
        create_url = urljoin(self.base_url, '/admin/reports/create')
        response = self.session.get(create_url)
        
        print(f"创建报告页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查表单
            form = soup.find('form')
            if form:
                print("✅ 找到创建报告表单")
                
                # 检查必要的输入字段
                required_fields = ['project_name', 'official_website', 'creator_name']
                found_fields = []
                
                for field in required_fields:
                    if soup.find('input', {'name': field}) or soup.find('textarea', {'name': field}):
                        found_fields.append(field)
                        
                print(f"✅ 找到字段: {found_fields}")
                return len(found_fields) >= 2  # 至少要有2个必要字段
            else:
                print("❌ 未找到创建报告表单")
                return False
        elif response.status_code == 404:
            print("⚠️ 创建报告页面不存在 (404)")
            return False
        else:
            print(f"❌ 创建报告页面访问失败，状态码: {response.status_code}")
            return False
            
    def run_all_tests(self):
        """运行所有管理员功能测试"""
        print("🚀 开始管理员功能全面测试...")
        
        # 登录
        if not self.login_admin():
            return False
            
        # 运行各项测试
        tests = [
            ("仪表板", self.test_dashboard),
            ("报告管理", self.test_reports_management),
            ("发布/取消发布", self.test_publish_unpublish),
            ("请求管理", self.test_requests_management),
            ("创建报告页面", self.test_create_report_page)
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
                status = "✅ 通过" if result else "❌ 失败"
                print(f"\n{test_name}测试结果: {status}")
            except Exception as e:
                print(f"\n{test_name}测试异常: {e}")
                results.append((test_name, False))
        
        # 总结
        print(f"\n{'='*50}")
        print("管理员功能测试总结")
        print('='*50)
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        print(f"\n总计: {passed}/{total} 通过")
        
        return passed == total

if __name__ == "__main__":
    # 检查服务器是否运行
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = AdminFunctionsTest()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)
