#!/usr/bin/env python3
"""
测试Supabase连接和配置
"""

import os
import sys
from dotenv import load_dotenv

load_dotenv()
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_supabase_connection():
    """测试Supabase连接"""
    print("🔍 测试Supabase连接...")
    
    # 检查环境变量
    required_vars = ['SUPABASE_URL', 'SUPABASE_KEY', 'SUPABASE_SERVICE_KEY']
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        elif value in ['your-project-id.supabase.co', 'your-anon-key', 'your-service-role-key']:
            missing_vars.append(f"{var} (使用了示例值)")
    
    if missing_vars:
        print("❌ 环境变量配置问题:")
        for var in missing_vars:
            print(f"   - {var}")
        return False
    
    print("✅ 环境变量配置正确")
    
    try:
        from app.services.database import db_service
        
        # 测试基本连接
        if db_service.use_mock:
            print("⚠️  当前使用模拟数据库，请配置真实的Supabase信息")
            return False
        
        # 测试数据库连接
        if not db_service.test_connection():
            print("❌ 数据库连接失败")
            return False
        
        print("✅ 数据库连接成功")
        
        # 测试表是否存在
        tables = ['admin_users', 'research_reports', 'user_requests']
        for table in tables:
            try:
                result = db_service.execute_query(table, 'select', use_service_key=True)
                print(f"✅ 表 {table} 存在")
            except Exception as e:
                print(f"❌ 表 {table} 不存在或无法访问: {e}")
                return False
        
        print("🎉 Supabase配置测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def test_app_with_supabase():
    """测试应用与Supabase的集成"""
    print("\n🧪 测试应用集成...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 测试健康检查
            response = client.get('/health')
            if response.status_code == 200:
                print("✅ 健康检查端点正常")
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
            
            # 测试首页
            response = client.get('/')
            if response.status_code == 200:
                print("✅ 首页加载正常")
            else:
                print(f"❌ 首页加载失败: {response.status_code}")
                return False
            
            # 测试管理员登录页面
            response = client.get('/admin/login')
            if response.status_code == 200:
                print("✅ 管理员登录页面正常")
            else:
                print(f"❌ 管理员登录页面失败: {response.status_code}")
                return False
        
        print("✅ 应用集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 应用测试失败: {e}")
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("🔧 Supabase 连接测试")
    print("=" * 60)
    
    success1 = test_supabase_connection()
    success2 = test_app_with_supabase() if success1 else False
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过！可以开始部署了")
        print("\n📋 下一步:")
        print("1. 运行 python setup_supabase.py 初始化数据")
        print("2. 上传示例文件到Supabase Storage")
        print("3. 部署到云平台 (Vercel/Railway/Render)")
    else:
        print("❌ 测试失败，请检查配置")
        print("\n🔧 解决方案:")
        print("1. 确认 .env 文件中的Supabase配置正确")
        print("2. 确认已在Supabase中创建了数据库表")
        print("3. 确认已设置了正确的RLS策略")
    print("=" * 60)
