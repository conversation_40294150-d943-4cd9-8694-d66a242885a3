#!/usr/bin/env python3
"""
管理员功能完整性测试
验证所有管理员功能是否正常工作
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_admin_templates_javascript():
    """测试管理员模板的JavaScript语法"""
    print("测试管理员模板JavaScript语法...")
    
    try:
        import re
        
        # 检查的模板文件
        template_files = [
            'templates/admin/reports.html',
            'templates/admin/requests.html',
            'templates/admin/create_report.html'
        ]
        
        for template_file in template_files:
            print(f"  检查 {template_file}...")
            
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查JavaScript语法问题
            issues = []
            
            # 检查onclick属性中的引号问题
            onclick_patterns = re.findall(r'onclick="([^"]*)"', content)
            for pattern in onclick_patterns:
                # 检查是否有未转义的单引号
                if "'" in pattern and not pattern.count("'") % 2 == 0:
                    issues.append(f"可能的引号问题: {pattern}")
                
                # 检查是否使用了tojson过滤器
                if "{{" in pattern and "|tojson" not in pattern and "'" in pattern:
                    issues.append(f"建议使用tojson过滤器: {pattern}")
            
            # 检查是否有CSRF token支持
            if 'getCSRFToken' in content and 'csrf-token' not in content:
                issues.append("使用了getCSRFToken但可能缺少CSRF token meta标签")
            
            if issues:
                print(f"    发现问题:")
                for issue in issues:
                    print(f"      - {issue}")
            else:
                print(f"    ✓ 语法检查通过")
        
        print("✓ JavaScript语法检查完成")
        return True
        
    except Exception as e:
        print(f"✗ JavaScript语法检查失败: {e}")
        return False

def test_admin_routes_exist():
    """测试管理员路由是否存在"""
    print("测试管理员路由...")
    
    try:
        from app import create_app
        app = create_app()
        
        # 检查的路由
        required_routes = [
            '/admin/login',
            '/admin/dashboard', 
            '/admin/reports',
            '/admin/reports/create',
            '/admin/requests'
        ]
        
        # 新增的API路由
        api_routes = [
            '/admin/reports/<report_id>/delete',
            '/admin/reports/<report_id>/status',
            '/admin/requests/<request_id>/status'
        ]
        
        # 获取所有路由
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(rule.rule)
        
        # 检查基础路由
        missing_routes = []
        for route in required_routes:
            if route not in routes:
                missing_routes.append(route)
            else:
                print(f"  ✓ {route}")
        
        # 检查API路由（使用模式匹配）
        for api_route in api_routes:
            pattern = api_route.replace('<report_id>', '[^/]+').replace('<request_id>', '[^/]+')
            found = any(re.match(pattern.replace('/', r'\/'), route) for route in routes)
            if found:
                print(f"  ✓ {api_route}")
            else:
                missing_routes.append(api_route)
        
        if missing_routes:
            print(f"  ✗ 缺少路由:")
            for route in missing_routes:
                print(f"    - {route}")
            return False
        
        print("✓ 所有管理员路由存在")
        return True
        
    except Exception as e:
        print(f"✗ 路由检查失败: {e}")
        return False

def test_model_methods():
    """测试模型方法是否存在"""
    print("测试模型方法...")
    
    try:
        from app.models.research_report import ResearchReport
        from app.models.user_request import UserRequest
        
        # 检查ResearchReport方法
        report_methods = [
            'delete',
            'update_status',
            'get_all_reports',
            'get_published_reports',
            'get_total_count'
        ]
        
        for method in report_methods:
            if hasattr(ResearchReport, method):
                print(f"  ✓ ResearchReport.{method}")
            else:
                print(f"  ✗ 缺少 ResearchReport.{method}")
                return False
        
        # 检查UserRequest方法
        request_methods = [
            'update_status',
            'get_all_requests',
            'get_pending_count'
        ]
        
        for method in request_methods:
            if hasattr(UserRequest, method):
                print(f"  ✓ UserRequest.{method}")
            else:
                print(f"  ✗ 缺少 UserRequest.{method}")
                return False
        
        print("✓ 所有模型方法存在")
        return True
        
    except Exception as e:
        print(f"✗ 模型方法检查失败: {e}")
        return False

def test_csrf_token_support():
    """测试CSRF token支持"""
    print("测试CSRF token支持...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.test_client() as client:
            # 测试管理员基础模板是否包含CSRF token
            response = client.get('/admin/login')
            if response.status_code == 200:
                content = response.get_data(as_text=True)
                if 'csrf-token' in content:
                    print("  ✓ CSRF token meta标签存在")
                else:
                    print("  ✗ 缺少CSRF token meta标签")
                    return False
            else:
                print(f"  ✗ 无法访问登录页面: {response.status_code}")
                return False
        
        print("✓ CSRF token支持正常")
        return True
        
    except Exception as e:
        print(f"✗ CSRF token测试失败: {e}")
        return False

def test_template_rendering():
    """测试模板渲染"""
    print("测试模板渲染...")
    
    try:
        from app import create_app
        from flask import render_template
        
        app = create_app()
        
        with app.app_context():
            with app.test_request_context():
                # 测试报告页面模板
                mock_reports = [{
                    'id': 'test-id',
                    'project_name': "Test Project with 'quotes'",
                    'creator_name': 'Test Creator',
                    'official_website': 'https://example.com',
                    'is_published': True,
                    'created_at': '2024-01-15T10:30:00Z',
                    'description': 'Test description'
                }]
                
                mock_pagination = {
                    'total': 1,
                    'total_pages': 1,
                    'page': 1,
                    'has_prev': False,
                    'has_next': False
                }
                
                rendered = render_template('admin/reports.html', 
                                         reports=mock_reports, 
                                         pagination=mock_pagination)
                
                # 检查是否正确处理了特殊字符
                if 'Test Project with' in rendered:
                    print("  ✓ 报告模板渲染正常")
                else:
                    print("  ✗ 报告模板渲染异常")
                    return False
                
                # 测试请求页面模板
                mock_requests = [{
                    'id': 'test-req-id',
                    'project_name': 'Test Request',
                    'user_name': 'Test User',
                    'user_email': '<EMAIL>',
                    'status': 'pending',
                    'created_at': '2024-01-15T10:30:00Z'
                }]
                
                rendered = render_template('admin/requests.html',
                                         requests=mock_requests,
                                         pagination=mock_pagination,
                                         status_filter='all')
                
                if 'Test Request' in rendered:
                    print("  ✓ 请求模板渲染正常")
                else:
                    print("  ✗ 请求模板渲染异常")
                    return False
        
        print("✓ 模板渲染测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 模板渲染测试失败: {e}")
        return False

def run_admin_functionality_tests():
    """运行所有管理员功能测试"""
    print("=" * 60)
    print("管理员功能完整性测试")
    print("=" * 60)
    
    tests = [
        ("JavaScript语法检查", test_admin_templates_javascript),
        ("管理员路由检查", test_admin_routes_exist),
        ("模型方法检查", test_model_methods),
        ("CSRF token支持", test_csrf_token_support),
        ("模板渲染测试", test_template_rendering)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试出错: {test_name} - {e}")
    
    print("\n" + "=" * 60)
    print(f"管理员功能测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有管理员功能测试通过！")
        print("\n✅ 修复的问题:")
        print("  - JavaScript语法错误（引号转义）")
        print("  - 添加了实际的删除和状态更新功能")
        print("  - 改进了错误处理和用户反馈")
        print("  - 添加了CSRF保护")
        print("  - 修复了模板渲染问题")
        
        print("\n🚀 现在可用的功能:")
        print("  - 报告删除（带确认）")
        print("  - 报告发布/取消发布")
        print("  - 请求状态更新")
        print("  - 加载状态指示")
        print("  - 错误处理和提示")
    else:
        print("❌ 部分管理员功能测试失败")
    
    return passed == total

if __name__ == '__main__':
    import re  # 添加re模块导入
    success = run_admin_functionality_tests()
    sys.exit(0 if success else 1)
