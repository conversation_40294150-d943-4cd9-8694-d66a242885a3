#!/usr/bin/env python3
"""
调试当前用户信息
"""

import os
import sys
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def debug_current_user():
    """调试当前用户信息"""
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    # 登录管理员
    print("🔐 登录管理员...")
    login_url = urljoin(base_url, '/admin/login')
    response = session.get(login_url)
    
    soup = BeautifulSoup(response.text, 'html.parser')
    csrf_meta = soup.find('meta', {'name': 'csrf-token'})
    csrf_token = csrf_meta.get('content') if csrf_meta else None
    
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123',
        'csrf_token': csrf_token
    }
    
    response = session.post(login_url, data=login_data, allow_redirects=True)
    
    if 'dashboard' not in response.url.lower():
        print("❌ 登录失败")
        return
        
    print("✅ 登录成功")
    
    # 访问管理员仪表板来获取用户信息
    dashboard_url = urljoin(base_url, '/admin/dashboard')
    response = session.get(dashboard_url)
    
    print(f"仪表板响应状态: {response.status_code}")
    
    # 检查数据库中的管理员用户
    print("\n🔍 检查数据库中的管理员用户...")
    try:
        from app.models.admin_user import AdminUser
        
        # 获取所有管理员用户
        admin_users = AdminUser.get_all()
        print(f"管理员用户数量: {len(admin_users)}")
        
        for user in admin_users:
            print(f"用户ID: {user.get('id')} (类型: {type(user.get('id'))})")
            print(f"用户邮箱: {user.get('email')}")
            print(f"用户名称: {user.get('name')}")
            print(f"是否激活: {user.get('is_active')}")
            print("---")
            
        # 尝试通过邮箱获取用户
        admin_user = AdminUser.get_by_email('<EMAIL>')
        if admin_user:
            print(f"通过邮箱获取的用户:")
            print(f"  ID: {admin_user.get('id')} (类型: {type(admin_user.get('id'))})")
            print(f"  邮箱: {admin_user.get('email')}")
            print(f"  名称: {admin_user.get('name')}")
        else:
            print("❌ 无法通过邮箱获取管理员用户")
            
    except Exception as e:
        print(f"❌ 检查管理员用户时出错: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试创建报告时的用户ID
    print("\n🧪 测试报告创建...")
    try:
        from app.models.admin_user import AdminUser
        from app.models.research_report import ResearchReport
        
        # 获取管理员用户
        admin_user = AdminUser.get_by_email('<EMAIL>')
        if not admin_user:
            print("❌ 无法获取管理员用户")
            return
            
        user_id = admin_user.get('id')
        print(f"使用用户ID: {user_id} (类型: {type(user_id)})")
        
        # 尝试创建报告
        report_data = {
            'project_name': 'Debug User Test',
            'official_website': 'https://example.com',
            'creator_name': 'Debug Tester',
            'report_file_path': 'reports/debug_user_test.md',
            'analysis_file_path': 'analysis/debug_user_test.html',
            'description': '调试用户ID测试报告',
            'created_by': user_id,  # 使用实际的用户ID
            'is_published': True
        }
        
        print(f"报告数据: {report_data}")
        
        result = ResearchReport.create(report_data)
        if result:
            print(f"✅ 报告创建成功: {result.get('id')}")
        else:
            print("❌ 报告创建失败")
            
    except Exception as e:
        print(f"❌ 测试报告创建时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_current_user()
