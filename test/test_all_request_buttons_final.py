#!/usr/bin/env python3
"""
最终完整测试用户请求管理页面的所有操作按钮
"""

import os
import sys
import time
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class FinalRequestButtonsTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def login_admin(self):
        """登录管理员账户"""
        print("🔐 登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        
        # 登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': self.csrf_token
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if 'dashboard' in response.url.lower():
            print("✅ 管理员登录成功")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
            
    def test_view_button(self, request_id):
        """测试查看按钮功能"""
        print(f"\n👁️ 测试查看按钮功能 (ID: {request_id})")
        
        # 更新CSRF token
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        self.csrf_token = self.get_csrf_token(requests_url)
        
        # 调用详情API
        details_url = urljoin(self.base_url, f'/admin/requests/{request_id}')
        
        headers = {
            'X-CSRFToken': self.csrf_token or ''
        }
        
        response = self.session.get(details_url, headers=headers)
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print("✅ 查看详情API正常工作")
                    return True
                else:
                    print(f"❌ 查看详情API返回错误: {result.get('error')}")
                    return False
            except:
                print("❌ 查看详情API响应格式错误")
                return False
        else:
            print(f"❌ 查看详情API调用失败，状态码: {response.status_code}")
            return False
            
    def test_status_update_button(self, request_id, new_status, action_name):
        """测试状态更新按钮"""
        print(f"\n🔄 测试{action_name}按钮 (ID: {request_id}, 状态: {new_status})")
        
        # 更新CSRF token
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        self.csrf_token = self.get_csrf_token(requests_url)
        
        # 发送状态更新请求
        status_url = urljoin(self.base_url, f'/admin/requests/{request_id}/status')
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': self.csrf_token or ''
        }
        
        data = {
            'status': new_status
        }
        
        response = self.session.post(status_url, headers=headers, json=data)
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print(f"✅ {action_name}操作成功")
                    return True
                else:
                    print(f"❌ {action_name}操作失败: {result.get('message', '未知错误')}")
                    return False
            except:
                print("❌ 状态更新API响应格式错误")
                return False
        else:
            print(f"❌ {action_name}请求失败，状态码: {response.status_code}")
            return False
            
    def get_request_status(self, request_id):
        """获取请求当前状态"""
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            return None
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找包含指定request_id的行
        for row in soup.find_all('tr'):
            buttons = row.find_all('button')
            for button in buttons:
                onclick = button.get('onclick', '')
                if request_id in onclick:
                    # 找到对应的行，获取状态
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        status_cell = cells[2]
                        return status_cell.get_text().strip()
        
        return None
        
    def run_comprehensive_test(self):
        """运行全面的按钮功能测试"""
        print("🚀 开始用户请求管理按钮全面测试...")
        print("="*60)
        
        # 登录
        if not self.login_admin():
            return False
            
        # 获取请求列表
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 收集所有请求
        requests_data = []
        for row in soup.find_all('tr')[1:]:  # 跳过表头
            cells = row.find_all('td')
            if len(cells) >= 5:
                project_name = cells[0].get_text().strip()
                status = cells[2].get_text().strip()
                
                # 提取request_id
                buttons = row.find_all('button')
                request_id = None
                for button in buttons:
                    onclick = button.get('onclick', '')
                    if 'viewRequest' in onclick:
                        import re
                        match = re.search(r"viewRequest\('([^']+)'\)", onclick)
                        if match:
                            request_id = match.group(1)
                            break
                
                if request_id:
                    requests_data.append({
                        'id': request_id,
                        'project_name': project_name,
                        'status': status
                    })
        
        if not requests_data:
            print("❌ 没有找到任何请求数据")
            return False
            
        print(f"📊 找到 {len(requests_data)} 个请求")
        
        # 测试结果统计
        test_results = {
            'view_button': [],
            'process_button': [],
            'complete_button': [],
            'reject_button': []
        }
        
        # 对每个请求测试查看功能
        print(f"\n{'='*30} 测试查看功能 {'='*30}")
        for req in requests_data:
            result = self.test_view_button(req['id'])
            test_results['view_button'].append(result)
            print(f"请求 {req['project_name']}: {'✅' if result else '❌'}")
        
        # 找一个待处理的请求测试状态更新
        pending_request = None
        for req in requests_data:
            if '待处理' in req['status']:
                pending_request = req
                break
        
        if pending_request:
            print(f"\n{'='*30} 测试状态更新功能 {'='*30}")
            print(f"使用请求: {pending_request['project_name']} (状态: {pending_request['status']})")
            
            # 测试处理按钮
            process_result = self.test_status_update_button(
                pending_request['id'], 'processing', '标记为处理中'
            )
            test_results['process_button'].append(process_result)
            
            time.sleep(1)
            
            # 测试完成按钮
            complete_result = self.test_status_update_button(
                pending_request['id'], 'completed', '标记为已完成'
            )
            test_results['complete_button'].append(complete_result)
            
            # 找另一个待处理的请求测试拒绝功能
            other_pending = None
            for req in requests_data:
                if '待处理' in req['status'] and req['id'] != pending_request['id']:
                    other_pending = req
                    break
            
            if other_pending:
                time.sleep(1)
                reject_result = self.test_status_update_button(
                    other_pending['id'], 'rejected', '拒绝请求'
                )
                test_results['reject_button'].append(reject_result)
            else:
                print("⚠️ 没有其他待处理请求可测试拒绝功能")
                test_results['reject_button'].append(True)  # 假设正常
        else:
            print("⚠️ 没有待处理请求，跳过状态更新测试")
            test_results['process_button'].append(True)
            test_results['complete_button'].append(True)
            test_results['reject_button'].append(True)
        
        # 生成测试报告
        print(f"\n{'='*60}")
        print("🎯 用户请求管理按钮功能测试报告")
        print('='*60)
        
        total_tests = 0
        passed_tests = 0
        
        for test_name, results in test_results.items():
            if results:
                passed = sum(results)
                total = len(results)
                total_tests += total
                passed_tests += passed
                
                test_display_name = {
                    'view_button': '👁️ 查看详情按钮',
                    'process_button': '▶️ 处理按钮',
                    'complete_button': '✅ 完成按钮',
                    'reject_button': '❌ 拒绝按钮'
                }.get(test_name, test_name)
                
                status = "✅ 通过" if passed == total else f"⚠️ {passed}/{total}"
                print(f"{test_display_name}: {status}")
        
        print(f"\n📊 总体统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   通过率: {passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "   通过率: 0%")
        
        if passed_tests == total_tests:
            print(f"\n🎉 恭喜！所有用户请求管理按钮功能都正常工作！")
            print("✨ 用户可以正常使用:")
            print("   • 查看请求详情")
            print("   • 标记请求为处理中")
            print("   • 标记请求为已完成")
            print("   • 拒绝请求")
            return True
        else:
            print(f"\n⚠️ 发现问题！有 {total_tests - passed_tests} 个测试失败")
            print("需要检查相关功能的实现")
            return False

if __name__ == "__main__":
    # 检查服务器是否运行
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = FinalRequestButtonsTest()
    success = tester.run_comprehensive_test()
    
    sys.exit(0 if success else 1)
