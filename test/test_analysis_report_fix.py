#!/usr/bin/env python3
"""
测试分析和报告功能修复
验证项目列表页面的分析和报告按钮功能是否正常工作
"""

import requests
import sys
import os
from urllib.parse import urljoin
from bs4 import BeautifulSoup

def test_analysis_report_functionality():
    """测试分析和报告功能"""
    base_url = 'http://127.0.0.1:5001'
    
    print("🔧 测试分析和报告功能修复")
    print("=" * 60)
    
    try:
        # 1. 访问首页
        print("1. 访问项目列表页面...")
        response = requests.get(base_url)
        if response.status_code != 200:
            print(f"❌ 首页访问失败: {response.status_code}")
            return False
        
        soup = BeautifulSoup(response.text, 'html.parser')
        print("✅ 项目列表页面访问成功")
        
        # 2. 检查页面中是否有项目
        project_rows = soup.find_all('tr')[1:]  # 跳过表头
        if not project_rows:
            print("❌ 没有找到任何项目")
            return False
        
        print(f"✅ 找到 {len(project_rows)} 个项目")
        
        # 3. 查找分析和报告按钮
        print("\n2. 检查分析和报告按钮...")
        analysis_buttons = soup.find_all('a', href=lambda x: x and '/analysis' in x)
        report_buttons = soup.find_all('a', href=lambda x: x and '/report' in x and '/analysis' not in x)
        
        print(f"   找到 {len(analysis_buttons)} 个分析按钮")
        print(f"   找到 {len(report_buttons)} 个报告按钮")
        
        if not analysis_buttons:
            print("❌ 没有找到分析按钮")
            return False
        
        if not report_buttons:
            print("❌ 没有找到报告按钮")
            return False
        
        # 4. 测试分析功能
        print("\n3. 测试分析功能...")
        analysis_url = urljoin(base_url, analysis_buttons[0]['href'])
        print(f"   访问分析页面: {analysis_url}")
        
        analysis_response = requests.get(analysis_url)
        if analysis_response.status_code != 200:
            print(f"❌ 分析页面访问失败: {analysis_response.status_code}")
            return False
        
        # 检查分析页面内容
        analysis_content = analysis_response.text.lower()
        has_charts = any(keyword in analysis_content for keyword in ['chart.js', 'canvas', 'chart'])
        has_analysis_content = any(keyword in analysis_content for keyword in ['分析', 'analysis'])
        
        if has_charts:
            print("✅ 分析页面包含图表功能")
        else:
            print("⚠️  分析页面可能缺少图表功能")
        
        if has_analysis_content:
            print("✅ 分析页面包含分析内容")
        else:
            print("⚠️  分析页面可能缺少分析内容")
        
        # 5. 测试报告功能
        print("\n4. 测试报告功能...")
        report_url = urljoin(base_url, report_buttons[0]['href'])
        print(f"   访问报告页面: {report_url}")
        
        report_response = requests.get(report_url)
        if report_response.status_code != 200:
            print(f"❌ 报告页面访问失败: {report_response.status_code}")
            return False
        
        # 检查报告页面内容
        report_soup = BeautifulSoup(report_response.text, 'html.parser')
        content_div = report_soup.find('div', class_='report-content')
        
        if content_div and content_div.get_text().strip():
            print("✅ 报告页面包含内容")
            content_length = len(content_div.get_text().strip())
            print(f"   内容长度: {content_length} 字符")
        else:
            print("❌ 报告页面缺少内容")
            return False
        
        # 6. 检查页面结构和导航
        print("\n5. 检查页面结构...")
        
        # 检查分析页面标题
        analysis_soup = BeautifulSoup(analysis_response.text, 'html.parser')
        analysis_title = analysis_soup.find('title')
        if analysis_title and '分析' in analysis_title.get_text():
            print("✅ 分析页面标题正确")
        else:
            print("⚠️  分析页面标题可能有问题")
        
        # 检查报告页面标题
        report_title = report_soup.find('title')
        if report_title and '报告' in report_title.get_text():
            print("✅ 报告页面标题正确")
        else:
            print("⚠️  报告页面标题可能有问题")
        
        # 检查返回按钮
        back_buttons_analysis = analysis_soup.find_all('a', href='/')
        back_buttons_report = report_soup.find_all('a', href='/')
        
        if back_buttons_analysis:
            print("✅ 分析页面有返回按钮")
        else:
            print("⚠️  分析页面可能缺少返回按钮")
        
        if back_buttons_report:
            print("✅ 报告页面有返回按钮")
        else:
            print("⚠️  报告页面可能缺少返回按钮")
        
        # 7. 检查日期时间显示
        print("\n6. 检查日期时间显示...")
        
        # 检查是否有moment错误
        if 'moment' in analysis_response.text and 'undefined' in analysis_response.text:
            print("❌ 分析页面仍有moment错误")
            return False
        
        if 'moment' in report_response.text and 'undefined' in report_response.text:
            print("❌ 报告页面仍有moment错误")
            return False
        
        print("✅ 日期时间显示正常，无moment错误")
        
        print("\n" + "=" * 60)
        print("🎉 分析和报告功能修复验证完成！")
        print("✅ 所有功能都正常工作")
        print("\n修复内容总结:")
        print("  ✓ 创建了缺失的分析文件")
        print("  ✓ 创建了缺失的报告文件")
        print("  ✓ 修复了moment.js依赖问题")
        print("  ✓ 使用自定义日期过滤器替换moment")
        print("  ✓ 分析和报告按钮都能正常工作")
        print("  ✓ 页面内容正常显示")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用程序正在运行")
        print("   运行命令: PORT=5001 python run.py")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_analysis_report_functionality()
    sys.exit(0 if success else 1)
