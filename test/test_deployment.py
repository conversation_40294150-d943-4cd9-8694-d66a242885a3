#!/usr/bin/env python3
"""
部署验证测试脚本
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_critical_files():
    """测试关键文件是否存在"""
    print("检查关键文件...")
    
    critical_files = [
        'api/run.py',  # Vercel入口文件
        'vercel.json',  # Vercel配置
        'requirements.txt',  # 依赖文件
        'app/__init__.py',  # Flask应用
        'templates/admin/reports.html',  # 报错的模板文件
        'templates/admin/create_report.html',
        'templates/admin/requests.html',
        'templates/admin/dashboard.html',
        'templates/admin/login.html',
        'templates/admin/base.html'
    ]
    
    missing_files = []
    for file_path in critical_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    if missing_files:
        print("✗ 缺少以下关键文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print("✓ 所有关键文件都存在")
        return True

def test_template_syntax():
    """测试模板语法"""
    print("检查模板语法...")
    
    template_files = [
        'templates/admin/reports.html',
        'templates/admin/create_report.html',
        'templates/admin/requests.html'
    ]
    
    for template_file in template_files:
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 基本语法检查
            if '{% extends' not in content:
                print(f"✗ {template_file} 缺少 extends 声明")
                return False
            
            if '{% block content %}' not in content:
                print(f"✗ {template_file} 缺少 content 块")
                return False
            
            if '{% endblock %}' not in content:
                print(f"✗ {template_file} 缺少 endblock")
                return False
            
            print(f"✓ {template_file} 语法正常")
            
        except Exception as e:
            print(f"✗ {template_file} 读取失败: {e}")
            return False
    
    return True

def test_app_import():
    """测试应用导入"""
    print("测试应用导入...")
    
    try:
        from app import create_app
        app = create_app()
        print("✓ Flask应用创建成功")
        
        # 测试基本路由
        with app.test_client() as client:
            response = client.get('/')
            if response.status_code == 200:
                print("✓ 首页路由正常")
            else:
                print(f"✗ 首页路由异常: {response.status_code}")
                return False
            
            response = client.get('/admin/login')
            if response.status_code == 200:
                print("✓ 管理员登录路由正常")
            else:
                print(f"✗ 管理员登录路由异常: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 应用导入失败: {e}")
        return False

def test_vercel_config():
    """测试Vercel配置"""
    print("检查Vercel配置...")
    
    try:
        import json
        with open('vercel.json', 'r') as f:
            config = json.load(f)
        
        # 检查必要的配置
        if 'rewrites' not in config:
            print("✗ vercel.json 缺少 rewrites 配置")
            return False
        
        if 'functions' not in config:
            print("✗ vercel.json 缺少 functions 配置")
            return False
        
        if 'api/run.py' not in config['functions']:
            print("✗ vercel.json 缺少 api/run.py 函数配置")
            return False
        
        print("✓ Vercel配置正常")
        return True
        
    except Exception as e:
        print(f"✗ Vercel配置检查失败: {e}")
        return False

def test_environment_variables():
    """测试环境变量"""
    print("检查环境变量...")
    
    required_vars = ['SUPABASE_URL', 'SUPABASE_KEY', 'FLASK_SECRET_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("✗ 缺少以下环境变量:")
        for var in missing_vars:
            print(f"  - {var}")
        print("注意：在Vercel部署时需要在环境变量中设置这些值")
        return False
    else:
        print("✓ 环境变量配置正常")
        return True

def test_admin_routes_exist():
    """测试管理员路由是否存在"""
    print("检查管理员路由...")
    
    try:
        from app import create_app
        app = create_app()
        
        # 获取所有路由
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(rule.rule)
        
        required_routes = [
            '/admin/login',
            '/admin/dashboard',
            '/admin/reports',
            '/admin/reports/create',
            '/admin/requests'
        ]
        
        missing_routes = []
        for route in required_routes:
            if route not in routes:
                missing_routes.append(route)
            else:
                print(f"✓ {route}")
        
        if missing_routes:
            print("✗ 缺少以下路由:")
            for route in missing_routes:
                print(f"  - {route}")
            return False
        
        print("✓ 所有管理员路由都存在")
        return True
        
    except Exception as e:
        print(f"✗ 路由检查失败: {e}")
        return False

def run_deployment_tests():
    """运行部署验证测试"""
    print("=" * 60)
    print("部署验证测试")
    print("=" * 60)
    
    tests = [
        ("关键文件", test_critical_files),
        ("模板语法", test_template_syntax),
        ("应用导入", test_app_import),
        ("Vercel配置", test_vercel_config),
        ("环境变量", test_environment_variables),
        ("管理员路由", test_admin_routes_exist)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试出错: {test_name} - {e}")
    
    print("\n" + "=" * 60)
    print(f"部署验证测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有部署验证测试通过！")
        print("\n部署建议:")
        print("1. 确保在Vercel中设置了所有必要的环境变量")
        print("2. 确保Supabase数据库已正确配置")
        print("3. 检查RLS策略是否正确设置")
        print("\n现在可以安全地部署到Vercel了！")
    else:
        print("✗ 部分部署验证测试失败，请先修复问题再部署。")
    
    return passed == total

if __name__ == '__main__':
    success = run_deployment_tests()
    sys.exit(0 if success else 1)
