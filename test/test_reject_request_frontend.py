#!/usr/bin/env python3
"""
测试拒绝请求的前端行为
"""

import os
import sys
import requests
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class RejectRequestFrontendTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def login_admin(self):
        """登录管理员账户"""
        print("🔐 登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        
        # 登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': self.csrf_token
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if 'dashboard' in response.url.lower():
            print("✅ 管理员登录成功")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
            
    def test_reject_confirmation_message(self):
        """测试拒绝确认消息"""
        print("\n💬 测试拒绝确认消息...")
        
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            print("❌ 无法访问请求管理页面")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找updateStatus函数中的拒绝确认消息
        scripts = soup.find_all('script')
        reject_message_found = False
        delete_warning_found = False
        
        for script in scripts:
            if script.string:
                script_content = script.string
                if 'case \'rejected\':' in script_content:
                    if '确定要拒绝此请求吗' in script_content:
                        reject_message_found = True
                    if '无法恢复' in script_content:
                        delete_warning_found = True
                        
        if reject_message_found:
            print("✅ 找到拒绝确认消息")
        else:
            print("❌ 未找到拒绝确认消息")
            return False
            
        if delete_warning_found:
            print("✅ 找到删除警告信息")
        else:
            print("❌ 未找到删除警告信息")
            return False
            
        return True
        
    def test_reject_button_behavior(self):
        """测试拒绝按钮的JavaScript行为"""
        print("\n🔘 测试拒绝按钮JavaScript行为...")
        
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            print("❌ 无法访问请求管理页面")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 检查是否有拒绝按钮
        reject_buttons = soup.find_all('button', {'onclick': lambda x: x and 'rejected' in x})
        if reject_buttons:
            print(f"✅ 找到 {len(reject_buttons)} 个拒绝按钮")
        else:
            print("❌ 未找到拒绝按钮")
            return False
            
        # 检查JavaScript处理逻辑
        scripts = soup.find_all('script')
        row_removal_logic_found = False
        alert_function_found = False
        
        for script in scripts:
            if script.string:
                script_content = script.string
                # 检查行移除逻辑
                if 'row.remove()' in script_content and 'newStatus === \'rejected\'' in script_content:
                    row_removal_logic_found = True
                # 检查警告函数
                if 'function showAlert' in script_content:
                    alert_function_found = True
                    
        if row_removal_logic_found:
            print("✅ 找到行移除逻辑")
        else:
            print("❌ 未找到行移除逻辑")
            return False
            
        if alert_function_found:
            print("✅ 找到警告函数")
        else:
            print("❌ 未找到警告函数")
            return False
            
        return True
        
    def test_empty_state_handling(self):
        """测试空状态处理"""
        print("\n📭 测试空状态处理...")
        
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            print("❌ 无法访问请求管理页面")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 检查JavaScript中的空状态处理逻辑
        scripts = soup.find_all('script')
        empty_state_logic_found = False
        
        for script in scripts:
            if script.string:
                script_content = script.string
                # 检查空状态处理
                if 'tbody.children.length === 0' in script_content and '暂无请求数据' in script_content:
                    empty_state_logic_found = True
                    break
                    
        if empty_state_logic_found:
            print("✅ 找到空状态处理逻辑")
        else:
            print("❌ 未找到空状态处理逻辑")
            return False
            
        return True
        
    def test_visual_feedback(self):
        """测试视觉反馈"""
        print("\n🎨 测试视觉反馈...")
        
        requests_url = urljoin(self.base_url, '/admin/requests')
        response = self.session.get(requests_url)
        
        if response.status_code != 200:
            print("❌ 无法访问请求管理页面")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 检查JavaScript中的视觉反馈
        scripts = soup.find_all('script')
        transition_effect_found = False
        success_message_found = False
        
        for script in scripts:
            if script.string:
                script_content = script.string
                # 检查过渡效果
                if 'transition' in script_content and 'opacity' in script_content:
                    transition_effect_found = True
                # 检查成功消息
                if 'showAlert(\'success\'' in script_content and '请求已成功拒绝并删除' in script_content:
                    success_message_found = True
                    
        if transition_effect_found:
            print("✅ 找到过渡效果")
        else:
            print("❌ 未找到过渡效果")
            return False
            
        if success_message_found:
            print("✅ 找到成功消息")
        else:
            print("❌ 未找到成功消息")
            return False
            
        return True
        
    def run_frontend_test(self):
        """运行前端测试"""
        print("🚀 开始拒绝请求前端功能测试...")
        print("="*60)
        
        # 登录
        if not self.login_admin():
            return False
            
        # 测试各项前端功能
        confirmation_success = self.test_reject_confirmation_message()
        button_success = self.test_reject_button_behavior()
        empty_state_success = self.test_empty_state_handling()
        visual_success = self.test_visual_feedback()
        
        # 生成测试报告
        print(f"\n{'='*60}")
        print("拒绝请求前端功能测试报告")
        print('='*60)
        
        results = [
            ("确认消息", confirmation_success),
            ("按钮行为", button_success),
            ("空状态处理", empty_state_success),
            ("视觉反馈", visual_success)
        ]
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        overall_success = all(result for _, result in results)
        
        if overall_success:
            print(f"\n🎉 拒绝请求前端功能测试全部通过！")
            print("✨ 前端功能正常:")
            print("   • 显示正确的拒绝确认消息和删除警告")
            print("   • 拒绝后从页面移除请求行")
            print("   • 处理空状态显示")
            print("   • 提供流畅的视觉反馈")
            print("   • 显示成功操作消息")
        else:
            print(f"\n❌ 拒绝请求前端功能存在问题")
            
        return overall_success

if __name__ == "__main__":
    # 检查服务器是否运行
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = RejectRequestFrontendTest()
    success = tester.run_frontend_test()
    
    sys.exit(0 if success else 1)
