#!/usr/bin/env python3
"""
前端功能全面回归测试
测试所有面向用户的功能是否正常工作
"""

import os
import sys
import time
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup
import re

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class FrontendRegressionTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = f"{status} {test_name}"
        if message:
            result += f" - {message}"
        print(result)
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def test_public_pages(self):
        """测试公开页面"""
        print("\n🌐 测试公开页面功能...")
        
        # 测试首页
        try:
            response = self.session.get(self.base_url)
            self.log_test("首页访问", response.status_code == 200, 
                         f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                # 检查关键元素
                has_nav = soup.find('nav') is not None
                has_hero = soup.find(class_=re.compile(r'hero|jumbotron|banner')) is not None
                self.log_test("首页导航栏", has_nav)
                self.log_test("首页主要内容区域", has_hero)
                
        except Exception as e:
            self.log_test("首页访问", False, str(e))
            
        # 测试报告列表页
        try:
            response = self.session.get(urljoin(self.base_url, '/reports'))
            self.log_test("报告列表页访问", response.status_code == 200,
                         f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                # 检查是否有报告列表或空状态提示
                has_reports = soup.find(class_=re.compile(r'report|card')) is not None
                has_empty_state = soup.find(text=re.compile(r'暂无|没有|empty', re.I)) is not None
                self.log_test("报告列表内容", has_reports or has_empty_state)
                
        except Exception as e:
            self.log_test("报告列表页访问", False, str(e))
            
        # 测试提交请求页
        try:
            response = self.session.get(urljoin(self.base_url, '/submit'))
            self.log_test("提交请求页访问", response.status_code == 200,
                         f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                # 检查表单元素
                has_form = soup.find('form') is not None
                has_submit_btn = soup.find('button', type='submit') is not None or \
                               soup.find('input', type='submit') is not None
                self.log_test("提交请求表单", has_form and has_submit_btn)
                
        except Exception as e:
            self.log_test("提交请求页访问", False, str(e))
            
    def test_admin_login(self):
        """测试管理员登录功能"""
        print("\n🔐 测试管理员登录功能...")
        
        try:
            # 访问登录页面
            login_url = urljoin(self.base_url, '/admin/login')
            response = self.session.get(login_url)
            self.log_test("管理员登录页访问", response.status_code == 200,
                         f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 检查登录表单
                login_form = soup.find('form')
                email_input = soup.find('input', {'type': 'email'}) or \
                             soup.find('input', {'name': 'email'})
                password_input = soup.find('input', {'type': 'password'}) or \
                               soup.find('input', {'name': 'password'})
                
                self.log_test("登录表单存在", login_form is not None)
                self.log_test("邮箱输入框", email_input is not None)
                self.log_test("密码输入框", password_input is not None)
                
                # 检查CSRF token
                csrf_token = self.get_csrf_token(login_url)
                self.log_test("CSRF token设置", csrf_token is not None)
                self.csrf_token = csrf_token
                
        except Exception as e:
            self.log_test("管理员登录页测试", False, str(e))
            
    def test_admin_pages_access(self):
        """测试管理员页面访问（未登录状态）"""
        print("\n🚫 测试管理员页面访问控制...")
        
        admin_pages = [
            '/admin/dashboard',
            '/admin/reports',
            '/admin/requests',
            '/admin/create_report'
        ]
        
        for page in admin_pages:
            try:
                response = self.session.get(urljoin(self.base_url, page))
                # 应该重定向到登录页面或返回401/403
                is_protected = response.status_code in [302, 401, 403] or \
                              'login' in response.url.lower()
                self.log_test(f"管理员页面保护 {page}", is_protected,
                             f"状态码: {response.status_code}")
                             
            except Exception as e:
                self.log_test(f"管理员页面访问测试 {page}", False, str(e))
                
    def test_javascript_errors(self):
        """检查JavaScript语法错误"""
        print("\n🔧 检查JavaScript语法...")
        
        # 检查主要页面的JavaScript
        pages_to_check = [
            '/',
            '/reports',
            '/submit',
            '/admin/login'
        ]
        
        for page in pages_to_check:
            try:
                response = self.session.get(urljoin(self.base_url, page))
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 检查内联JavaScript
                    scripts = soup.find_all('script')
                    js_syntax_ok = True
                    
                    for script in scripts:
                        if script.string:
                            js_content = script.string
                            # 基本的JavaScript语法检查
                            if self.check_js_syntax_issues(js_content):
                                js_syntax_ok = False
                                break
                    
                    self.log_test(f"JavaScript语法检查 {page}", js_syntax_ok)
                    
            except Exception as e:
                self.log_test(f"JavaScript检查 {page}", False, str(e))
                
    def check_js_syntax_issues(self, js_content):
        """检查JavaScript语法问题"""
        # 检查常见的语法问题
        issues = [
            r"'[^']*'[^']*'",  # 未转义的单引号
            r'"[^"]*"[^"]*"',  # 未转义的双引号
            r'function\s+\w+\([^)]*\)\s*{[^}]*}[^;]',  # 函数定义后缺少分号
        ]
        
        for pattern in issues:
            if re.search(pattern, js_content):
                return True
        return False
        
    def test_api_endpoints(self):
        """测试API端点"""
        print("\n🔌 测试API端点...")
        
        # 测试公开API
        try:
            response = self.session.get(urljoin(self.base_url, '/api/reports'))
            # API可能返回200（有数据）或404（未实现）
            api_accessible = response.status_code in [200, 404]
            self.log_test("公开API访问", api_accessible,
                         f"状态码: {response.status_code}")
                         
        except Exception as e:
            self.log_test("公开API测试", False, str(e))
            
    def test_static_resources(self):
        """测试静态资源加载"""
        print("\n📁 测试静态资源...")
        
        static_resources = [
            '/static/css/style.css',
            '/static/js/main.js',
            '/static/images/logo.png'
        ]
        
        for resource in static_resources:
            try:
                response = self.session.get(urljoin(self.base_url, resource))
                # 静态资源应该返回200或404（如果不存在）
                resource_ok = response.status_code in [200, 404]
                self.log_test(f"静态资源 {resource}", resource_ok,
                             f"状态码: {response.status_code}")
                             
            except Exception as e:
                self.log_test(f"静态资源测试 {resource}", False, str(e))
                
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始前端功能全面回归测试...")
        print(f"测试目标: {self.base_url}")
        
        # 检查服务器是否可访问
        try:
            response = self.session.get(self.base_url, timeout=5)
            if response.status_code != 200:
                print(f"❌ 服务器不可访问，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            return False
            
        print("✅ 服务器连接正常")
        
        # 运行各项测试
        self.test_public_pages()
        self.test_admin_login()
        self.test_admin_pages_access()
        self.test_javascript_errors()
        self.test_api_endpoints()
        self.test_static_resources()
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"\n📊 测试结果统计:")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")
                    
        return failed_tests == 0

if __name__ == "__main__":
    # 检查服务器是否运行
    import subprocess
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = FrontendRegressionTest()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)
