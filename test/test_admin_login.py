#!/usr/bin/env python3
"""
测试管理员登录功能
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_admin_user_model():
    """测试AdminUser模型"""
    print("🔍 测试AdminUser模型...")
    
    try:
        from app.models.admin_user import AdminUser
        from app.services.database import db_service
        
        print(f"数据库服务状态: {'Mock模式' if db_service.use_mock else '真实数据库'}")
        
        # 测试获取管理员用户
        admin_user = AdminUser.get_by_email('<EMAIL>')
        
        if admin_user:
            print("✅ 找到管理员用户")
            print(f"  ID: {admin_user.id}")
            print(f"  邮箱: {admin_user.email}")
            print(f"  姓名: {admin_user.name}")
            print(f"  激活状态: {admin_user.is_active}")
            print(f"  密码哈希: {admin_user.password_hash[:50]}...")
            
            # 测试密码验证
            password_correct = admin_user.check_password('admin123')
            print(f"  密码验证: {'✅ 正确' if password_correct else '❌ 错误'}")
            
            # 测试错误密码
            wrong_password = admin_user.check_password('wrong_password')
            print(f"  错误密码验证: {'❌ 应该失败但通过了' if wrong_password else '✅ 正确拒绝'}")
            
            return admin_user
        else:
            print("❌ 未找到管理员用户")
            return None
            
    except Exception as e:
        print(f"❌ AdminUser模型测试失败: {e}")
        return None

def test_login_logic():
    """测试登录逻辑"""
    print("\n🔐 测试登录逻辑...")
    
    try:
        from app.models.admin_user import AdminUser
        
        # 模拟登录过程
        email = '<EMAIL>'
        password = 'admin123'
        
        print(f"尝试登录: {email}")
        
        # 步骤1: 获取用户
        user = AdminUser.get_by_email(email)
        if not user:
            print("❌ 步骤1失败: 用户不存在")
            return False
        print("✅ 步骤1成功: 找到用户")
        
        # 步骤2: 验证密码
        if not user.check_password(password):
            print("❌ 步骤2失败: 密码验证失败")
            return False
        print("✅ 步骤2成功: 密码验证通过")
        
        # 步骤3: 检查用户状态
        if not user.is_active:
            print("❌ 步骤3失败: 用户未激活")
            return False
        print("✅ 步骤3成功: 用户已激活")
        
        print("✅ 登录逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 登录逻辑测试失败: {e}")
        return False

def test_database_direct():
    """直接测试数据库查询"""
    print("\n🗄️ 直接测试数据库查询...")
    
    try:
        from app.services.database import db_service
        
        # 直接查询管理员用户表
        result = db_service.execute_query(
            'admin_users',
            'select',
            filters={'email': '<EMAIL>'},
            use_service_key=True
        )
        
        if result.data:
            print("✅ 数据库查询成功")
            admin_data = result.data[0]
            print(f"  用户数据: {admin_data}")
            return admin_data
        else:
            print("❌ 数据库查询失败或无数据")
            return None
            
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return None

def test_password_hash():
    """测试密码哈希"""
    print("\n🔑 测试密码哈希...")
    
    try:
        from werkzeug.security import generate_password_hash, check_password_hash
        
        # 生成新的密码哈希
        password = 'admin123'
        new_hash = generate_password_hash(password)
        print(f"新生成的哈希: {new_hash}")
        
        # 验证新哈希
        is_valid = check_password_hash(new_hash, password)
        print(f"新哈希验证: {'✅ 通过' if is_valid else '❌ 失败'}")
        
        # 验证错误密码
        is_invalid = check_password_hash(new_hash, 'wrong_password')
        print(f"错误密码验证: {'❌ 应该失败但通过了' if is_invalid else '✅ 正确拒绝'}")
        
        return new_hash
        
    except Exception as e:
        print(f"❌ 密码哈希测试失败: {e}")
        return None

def fix_admin_password():
    """修复管理员密码"""
    print("\n🔧 修复管理员密码...")
    
    try:
        from app.services.database import db_service
        from werkzeug.security import generate_password_hash
        
        # 生成新的密码哈希
        new_hash = generate_password_hash('admin123')
        
        # 更新数据库中的密码
        result = db_service.execute_query(
            'admin_users',
            'update',
            data={'password_hash': new_hash},
            filters={'email': '<EMAIL>'},
            use_service_key=True
        )
        
        if result.data is not None:
            print("✅ 管理员密码更新成功")
            return True
        else:
            print("❌ 管理员密码更新失败")
            return False
            
    except Exception as e:
        print(f"❌ 修复管理员密码失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始管理员登录测试...")
    
    # 测试数据库连接
    try:
        from app.services.database import db_service
        if not db_service.test_connection():
            print("❌ 数据库连接失败")
            return False
        print("✅ 数据库连接正常")
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False
    
    # 运行各项测试
    db_data = test_database_direct()
    admin_user = test_admin_user_model()
    test_password_hash()
    login_success = test_login_logic()
    
    # 如果登录失败，尝试修复密码
    if not login_success and db_data:
        print("\n🔧 登录失败，尝试修复密码...")
        if fix_admin_password():
            print("密码修复完成，重新测试登录...")
            test_login_logic()
    
    print("\n📋 测试总结:")
    print("1. 数据库连接测试")
    print("2. 直接数据库查询测试")
    print("3. AdminUser模型测试")
    print("4. 密码哈希测试")
    print("5. 登录逻辑测试")
    print("6. 密码修复（如需要）")

if __name__ == "__main__":
    main()
