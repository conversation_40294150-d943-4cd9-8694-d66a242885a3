#!/usr/bin/env python3
"""
测试完成请求时上传文件功能
"""

import os
import sys
import time
import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup
import tempfile

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class CompleteRequestWithFilesTest:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def login_admin(self):
        """登录管理员账户"""
        print("🔐 登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        
        # 登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': self.csrf_token
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if 'dashboard' in response.url.lower():
            print("✅ 管理员登录成功")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
            
    def create_test_request(self):
        """创建测试请求"""
        try:
            from app.models.user_request import UserRequest
            
            request_data = {
                'user_email': '<EMAIL>',
                'project_name': 'Django',
                'official_website': 'https://djangoproject.com',
                'status': 'pending'
            }
            
            result = UserRequest.create(request_data)
            
            if result:
                print(f"✅ 测试请求创建成功")
                print(f"请求ID: {result.get('id')}")
                return result.get('id')
            else:
                print("❌ 测试请求创建失败")
                return None
                
        except Exception as e:
            print(f"❌ 创建测试请求时出错: {e}")
            return None
            
    def create_test_files(self):
        """创建测试文件"""
        # 创建测试Markdown文件
        md_content = """# Django项目研究报告

## 项目概述
Django是一个高级Python Web框架，鼓励快速开发和干净、实用的设计。

## 技术特点
- 快速开发
- 安全性高
- 可扩展性强

## 结论
Django是一个优秀的Web开发框架。
"""
        
        # 创建测试HTML文件
        html_content = """<!DOCTYPE html>
<html>
<head>
    <title>Django分析报告</title>
</head>
<body>
    <h1>Django项目分析</h1>
    <div id="chart">
        <p>这里是分析图表内容</p>
    </div>
    <script>
        console.log('Django分析报告加载完成');
    </script>
</body>
</html>
"""
        
        # 创建临时文件
        md_file = tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8')
        md_file.write(md_content)
        md_file.close()
        
        html_file = tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8')
        html_file.write(html_content)
        html_file.close()
        
        return md_file.name, html_file.name
        
    def test_complete_request_api(self, request_id):
        """测试完成请求API"""
        print(f"\n📤 测试完成请求API (ID: {request_id})")
        
        # 创建测试文件
        md_file_path, html_file_path = self.create_test_files()
        
        try:
            # 更新CSRF token
            requests_url = urljoin(self.base_url, '/admin/requests')
            response = self.session.get(requests_url)
            self.csrf_token = self.get_csrf_token(requests_url)
            
            # 准备文件和数据
            complete_url = urljoin(self.base_url, f'/admin/requests/{request_id}/complete')
            
            files = {
                'report_file': ('django_report.md', open(md_file_path, 'rb'), 'text/markdown'),
                'analysis_file': ('django_analysis.html', open(html_file_path, 'rb'), 'text/html')
            }
            
            data = {
                'creator_name': 'Test Admin',
                'description': '这是一个测试创建的Django项目研究报告',
                'admin_notes': '通过自动化测试完成'
            }
            
            headers = {
                'X-CSRFToken': self.csrf_token or ''
            }
            
            print(f"发送完成请求到: {complete_url}")
            print(f"CSRF Token: {self.csrf_token[:20]}..." if self.csrf_token else "无CSRF Token")
            
            response = self.session.post(complete_url, files=files, data=data, headers=headers)
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"响应数据: {result}")
                    
                    if result.get('success'):
                        print("✅ 完成请求API调用成功")
                        report_id = result.get('report_id')
                        if report_id:
                            print(f"✅ 报告创建成功，ID: {report_id}")
                        return True
                    else:
                        print(f"❌ 完成请求失败: {result.get('error', '未知错误')}")
                        return False
                except:
                    print("❌ 响应不是JSON格式")
                    print(f"响应内容: {response.text[:200]}...")
                    return False
            else:
                print(f"❌ 完成请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                return False
                
        finally:
            # 清理临时文件
            try:
                os.unlink(md_file_path)
                os.unlink(html_file_path)
            except:
                pass
                
    def verify_request_completed(self, request_id):
        """验证请求是否已完成"""
        print(f"\n🔍 验证请求完成状态 (ID: {request_id})")
        
        try:
            from app.models.user_request import UserRequest
            
            request_data = UserRequest.get_by_id(request_id)
            if request_data:
                status = request_data.get('status')
                print(f"请求状态: {status}")
                
                if status == 'completed':
                    print("✅ 请求状态已更新为已完成")
                    return True
                else:
                    print(f"❌ 请求状态未正确更新，当前状态: {status}")
                    return False
            else:
                print("❌ 无法获取请求数据")
                return False
                
        except Exception as e:
            print(f"❌ 验证请求状态时出错: {e}")
            return False
            
    def verify_report_created(self):
        """验证报告是否已创建"""
        print(f"\n📊 验证报告创建")
        
        try:
            from app.models.research_report import ResearchReport
            
            # 获取最新的报告
            reports, _ = ResearchReport.get_all_reports(page=1, per_page=5)
            
            if reports:
                latest_report = reports[0]
                if latest_report.get('project_name') == 'Django':
                    print("✅ Django报告已成功创建")
                    print(f"报告ID: {latest_report.get('id')}")
                    print(f"创建者: {latest_report.get('creator_name')}")
                    print(f"发布状态: {latest_report.get('is_published')}")
                    return True
                else:
                    print(f"❌ 最新报告不是Django项目: {latest_report.get('project_name')}")
                    return False
            else:
                print("❌ 没有找到任何报告")
                return False
                
        except Exception as e:
            print(f"❌ 验证报告创建时出错: {e}")
            return False
            
    def run_complete_test(self):
        """运行完整的测试"""
        print("🚀 开始完成请求上传文件功能测试...")
        print("="*60)
        
        # 登录管理员
        if not self.login_admin():
            return False
            
        # 创建测试请求
        request_id = self.create_test_request()
        if not request_id:
            return False
            
        # 测试完成请求API
        api_success = self.test_complete_request_api(request_id)
        
        if not api_success:
            print("❌ API测试失败，停止后续测试")
            return False
            
        # 等待一秒确保数据更新
        time.sleep(1)
        
        # 验证请求状态
        status_success = self.verify_request_completed(request_id)
        
        # 验证报告创建
        report_success = self.verify_report_created()
        
        # 生成测试报告
        print(f"\n{'='*60}")
        print("完成请求上传文件功能测试报告")
        print('='*60)
        
        results = [
            ("API调用", api_success),
            ("请求状态更新", status_success),
            ("报告创建", report_success)
        ]
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        overall_success = all(result for _, result in results)
        
        if overall_success:
            print(f"\n🎉 完成请求上传文件功能测试全部通过！")
            print("✨ 功能正常:")
            print("   • 管理员可以在完成请求时上传文件")
            print("   • 系统自动创建研究报告")
            print("   • 请求状态正确更新为已完成")
            print("   • 报告自动发布")
        else:
            print(f"\n❌ 完成请求上传文件功能存在问题")
            
        return overall_success

if __name__ == "__main__":
    # 检查服务器是否运行
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if not is_port_open('127.0.0.1', 5001):
        print("⚠️  服务器未在端口5001运行，请先启动服务器:")
        print("   PORT=5001 python run.py")
        sys.exit(1)
    
    # 运行测试
    tester = CompleteRequestWithFilesTest()
    success = tester.run_complete_test()
    
    sys.exit(0 if success else 1)
