#!/usr/bin/env python3
"""
逐步调试完成请求功能
"""

import os
import sys
import tempfile

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def debug_step_by_step():
    """逐步调试每个组件"""
    print("🔍 逐步调试完成请求功能...")
    
    # 1. 测试文件处理
    print("\n1. 测试文件处理...")
    try:
        from app.utils.file_handler import save_uploaded_file, allowed_file
        from werkzeug.datastructures import FileStorage
        import io
        
        # 创建模拟文件
        md_content = "# Test Report\n\nThis is a test."
        html_content = "<html><body><h1>Test</h1></body></html>"
        
        md_file = FileStorage(
            stream=io.BytesIO(md_content.encode('utf-8')),
            filename='test.md',
            content_type='text/markdown'
        )
        
        html_file = FileStorage(
            stream=io.BytesIO(html_content.encode('utf-8')),
            filename='test.html',
            content_type='text/html'
        )
        
        # 测试文件验证
        print(f"   MD文件验证: {allowed_file('test.md', ['md'])}")
        print(f"   HTML文件验证: {allowed_file('test.html', ['html', 'htm'])}")
        
        # 测试文件保存
        try:
            md_path = save_uploaded_file(md_file, 'reports')
            print(f"   MD文件保存成功: {md_path}")
        except Exception as e:
            print(f"   MD文件保存失败: {e}")
            
        try:
            html_path = save_uploaded_file(html_file, 'analysis')
            print(f"   HTML文件保存成功: {html_path}")
        except Exception as e:
            print(f"   HTML文件保存失败: {e}")
            
    except Exception as e:
        print(f"   文件处理测试失败: {e}")
    
    # 2. 测试数据库连接
    print("\n2. 测试数据库连接...")
    try:
        from app.services.database import db_service
        
        print(f"   数据库服务状态: {'Mock' if db_service.use_mock else '真实数据库'}")
        print(f"   数据库连接: {'✅' if db_service.test_connection() else '❌'}")
        
    except Exception as e:
        print(f"   数据库连接测试失败: {e}")
    
    # 3. 测试UserRequest模型
    print("\n3. 测试UserRequest模型...")
    try:
        from app.models.user_request import UserRequest
        
        # 创建测试请求
        request_data = {
            'user_email': '<EMAIL>',
            'project_name': 'Debug Step Test',
            'official_website': 'https://example.com',
            'status': 'pending'
        }
        
        result = UserRequest.create(request_data)
        if result:
            request_id = result.get('id')
            print(f"   请求创建成功: {request_id}")
            
            # 测试获取请求
            request_data = UserRequest.get_by_id(request_id)
            if request_data:
                print(f"   请求获取成功: {request_data['project_name']}")
            else:
                print("   请求获取失败")
                
        else:
            print("   请求创建失败")
            
    except Exception as e:
        print(f"   UserRequest模型测试失败: {e}")
    
    # 4. 测试ResearchReport模型
    print("\n4. 测试ResearchReport模型...")
    try:
        from app.models.research_report import ResearchReport
        
        # 创建测试报告
        report_data = {
            'project_name': 'Debug Step Report',
            'official_website': 'https://example.com',
            'creator_name': 'Debug Tester',
            'report_file_path': 'reports/debug_test.md',
            'analysis_file_path': 'analysis/debug_test.html',
            'description': '调试测试报告',
            'created_by': 'debug-user-id',
            'is_published': True
        }
        
        result = ResearchReport.create(report_data)
        if result:
            report_id = result.get('id')
            print(f"   报告创建成功: {report_id}")
        else:
            print("   报告创建失败")
            
    except Exception as e:
        print(f"   ResearchReport模型测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. 测试完整流程（不通过HTTP）
    print("\n5. 测试完整流程（直接调用）...")
    try:
        from app.models.user_request import UserRequest
        from app.models.research_report import ResearchReport
        from app.utils.file_handler import save_uploaded_file
        from werkzeug.datastructures import FileStorage
        import io
        
        # 创建测试请求
        request_data = {
            'user_email': '<EMAIL>',
            'project_name': 'Debug Full Test',
            'official_website': 'https://example.com',
            'status': 'pending'
        }
        
        request_result = UserRequest.create(request_data)
        if not request_result:
            print("   请求创建失败")
            return
            
        request_id = request_result.get('id')
        print(f"   步骤1: 请求创建成功 {request_id}")
        
        # 创建测试文件
        md_content = "# Debug Full Test Report\n\nThis is a full debug test."
        html_content = "<html><body><h1>Debug Full Test</h1></body></html>"
        
        md_file = FileStorage(
            stream=io.BytesIO(md_content.encode('utf-8')),
            filename='debug_full.md',
            content_type='text/markdown'
        )
        
        html_file = FileStorage(
            stream=io.BytesIO(html_content.encode('utf-8')),
            filename='debug_full.html',
            content_type='text/html'
        )
        
        # 保存文件
        try:
            report_path = save_uploaded_file(md_file, 'reports')
            analysis_path = save_uploaded_file(html_file, 'analysis')
            print(f"   步骤2: 文件保存成功")
            print(f"     报告文件: {report_path}")
            print(f"     分析文件: {analysis_path}")
        except Exception as e:
            print(f"   步骤2: 文件保存失败 - {e}")
            return
        
        # 创建报告
        report_data = {
            'project_name': request_result['project_name'],
            'official_website': request_result['official_website'],
            'creator_name': 'Debug Full Tester',
            'report_file_path': report_path,
            'analysis_file_path': analysis_path,
            'description': '完整调试测试报告',
            'created_by': 'debug-full-user-id',
            'is_published': True
        }
        
        try:
            created_report = ResearchReport.create(report_data)
            if created_report:
                print(f"   步骤3: 报告创建成功 {created_report.get('id')}")
            else:
                print("   步骤3: 报告创建失败")
                return
        except Exception as e:
            print(f"   步骤3: 报告创建失败 - {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 更新请求状态
        try:
            success = UserRequest.update_status(request_id, 'completed', 'debug-admin-id', '调试测试完成')
            if success:
                print(f"   步骤4: 请求状态更新成功")
            else:
                print("   步骤4: 请求状态更新失败")
        except Exception as e:
            print(f"   步骤4: 请求状态更新失败 - {e}")
            
        print("   ✅ 完整流程测试成功！")
        
    except Exception as e:
        print(f"   完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_step_by_step()
