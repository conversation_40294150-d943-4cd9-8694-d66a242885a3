{% extends "base.html" %}

{% block title %}{{ report.project_name }} - 分析页面 - 项目研究报告平台{% endblock %}

{% block extra_css %}
<style>
    .analysis-container {
        background: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        overflow: hidden;
    }
    
    .analysis-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
    }
    
    .analysis-content {
        min-height: 600px;
        padding: 0;
    }
    
    .analysis-content iframe {
        width: 100%;
        height: 100%;
        border: none;
        min-height: 600px;
    }
    
    .back-button {
        margin-bottom: 1rem;
    }
    
    .project-meta {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0.375rem;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .project-meta .meta-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }
    
    .project-meta .meta-item:last-child {
        margin-bottom: 0;
    }
    
    .project-meta .meta-label {
        font-weight: 500;
        opacity: 0.9;
    }
    
    .project-meta .meta-value {
        font-weight: 600;
    }
    
    .action-buttons {
        margin-top: 1.5rem;
    }
    
    .action-buttons .btn {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    @media (max-width: 768px) {
        .analysis-header {
            padding: 1.5rem 1rem;
        }
        
        .project-meta .meta-item {
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .action-buttons .btn {
            width: 100%;
            margin-right: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Back Button -->
        <div class="back-button">
            <a href="{{ url_for('public.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回列表
            </a>
        </div>

        <!-- Analysis Container -->
        <div class="analysis-container">
            <!-- Header -->
            <div class="analysis-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="h2 mb-0">
                            <i class="fas fa-chart-bar me-2"></i>{{ report.project_name }}
                        </h1>
                        <p class="mb-0 mt-2 opacity-75">交互式分析页面</p>
                    </div>
                    <div class="col-md-4">
                        <div class="project-meta">
                            <div class="meta-item">
                                <span class="meta-label">创建者:</span>
                                <span class="meta-value">{{ report.creator_name }}</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label">创建时间:</span>
                                <span class="meta-value">
                                    {{ report.created_at | datetime('%Y-%m-%d') if report.created_at else '未知' }}
                                </span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label">最后更新:</span>
                                <span class="meta-value">
                                    {{ report.updated_at | datetime('%Y-%m-%d') if report.updated_at else '未知' }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <a href="{{ url_for('public.view_report', report_id=report.id) }}" 
                               class="btn btn-light btn-sm">
                                <i class="fas fa-file-text me-1"></i>查看报告
                            </a>
                            {% if report.official_website %}
                            <a href="{{ report.official_website }}" target="_blank" 
                               class="btn btn-light btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i>官方网站
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Content -->
            <div class="analysis-content">
                {% if analysis_content %}
                    <!-- 如果是完整的HTML文档，使用iframe -->
                    {% if analysis_content.startswith('<!DOCTYPE') or analysis_content.startswith('<html') %}
                        <iframe srcdoc="{{ analysis_content | e }}" 
                                title="{{ report.project_name }} 分析页面"
                                sandbox="allow-scripts allow-same-origin allow-forms">
                        </iframe>
                    {% else %}
                        <!-- 如果是HTML片段，直接嵌入 -->
                        <div class="p-4">
                            {{ analysis_content | safe }}
                        </div>
                    {% endif %}
                {% else %}
                    <div class="p-4 text-center">
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                            <h3>分析内容不可用</h3>
                            <p class="text-muted">
                                抱歉，该项目的分析页面暂时无法显示。<br>
                                请稍后重试或联系管理员。
                            </p>
                            <a href="{{ url_for('public.view_report', report_id=report.id) }}" 
                               class="btn btn-primary">
                                <i class="fas fa-file-text me-1"></i>查看文字报告
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Additional Information -->
        <!-- {% if report.description %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>项目描述
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ report.description }}</p>
            </div>
        </div>
        {% endif %} -->

        <!-- Navigation -->
        <div class="d-flex justify-content-between mt-4">
            <a href="{{ url_for('public.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list me-1"></i>返回列表
            </a>
            <a href="{{ url_for('public.view_report', report_id=report.id) }}" class="btn btn-primary">
                <i class="fas fa-file-text me-1"></i>查看研究报告
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 监听iframe加载事件
    $('iframe').on('load', function() {
        console.log('Analysis page loaded successfully');
        
        // 可以在这里添加与iframe内容的交互逻辑
        try {
            const iframe = this;
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            // 添加一些基本的样式调整
            const style = iframeDoc.createElement('style');
            style.textContent = `
                body {
                    margin: 0;
                    padding: 20px;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                }
                
                /* 确保内容适应iframe */
                * {
                    box-sizing: border-box;
                }
            `;
            iframeDoc.head.appendChild(style);
            
        } catch (e) {
            // 跨域限制，无法访问iframe内容
            console.log('Cannot access iframe content due to cross-origin restrictions');
        }
    });
    
    // 处理iframe错误
    $('iframe').on('error', function() {
        console.error('Failed to load analysis page');
        $(this).parent().html(`
            <div class="p-4 text-center">
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle text-danger"></i>
                    <h3>加载失败</h3>
                    <p class="text-muted">分析页面加载失败，请刷新页面重试。</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-refresh me-1"></i>刷新页面
                    </button>
                </div>
            </div>
        `);
    });
    
    // 全屏功能
    $('.btn-fullscreen').on('click', function() {
        const iframe = $('iframe')[0];
        if (iframe.requestFullscreen) {
            iframe.requestFullscreen();
        } else if (iframe.webkitRequestFullscreen) {
            iframe.webkitRequestFullscreen();
        } else if (iframe.msRequestFullscreen) {
            iframe.msRequestFullscreen();
        }
    });
});
</script>
{% endblock %}
