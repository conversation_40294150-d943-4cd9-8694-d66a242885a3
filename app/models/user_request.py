from app.services.database import db_service
from typing import Optional, Dict, Any, List, Tuple
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class UserRequest:
    """用户请求模型"""
    
    @staticmethod
    def get_by_id(request_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取请求"""
        try:
            result = db_service.execute_query(
                'user_requests', 
                'select', 
                filters={'id': request_id}
            )
            
            if result.data and len(result.data) > 0:
                return result.data[0]
            return None
        
        except Exception as e:
            logger.error(f"Error getting request by ID {request_id}: {e}")
            return None
    
    @staticmethod
    def get_by_email_and_project(email: str, project_name: str) -> Optional[Dict[str, Any]]:
        """根据邮箱和项目名称获取请求（检查重复）"""
        try:
            result = db_service.execute_query(
                'user_requests', 
                'select', 
                filters={
                    'user_email': email,
                    'project_name': project_name
                }
            )
            
            if result.data and len(result.data) > 0:
                return result.data[0]
            return None
        
        except Exception as e:
            logger.error(f"Error getting request by email and project: {e}")
            return None
    
    @staticmethod
    def get_all_requests(page: int = 1, per_page: int = 10, 
                        status_filter: str = 'all') -> Tuple[List[Dict[str, Any]], int]:
        """获取所有请求列表（分页）"""
        try:
            offset = (page - 1) * per_page
            
            # 构建查询条件
            filters = {}
            if status_filter != 'all':
                filters['status'] = status_filter
            
            # 获取数据
            result = db_service.execute_query(
                'user_requests', 
                'select', 
                filters=filters if filters else None
            )
            
            if result.data:
                total_count = len(result.data)
                # 按创建时间倒序排序
                sorted_data = sorted(result.data, key=lambda x: x['created_at'], reverse=True)
                paginated_data = sorted_data[offset:offset + per_page]
                return paginated_data, total_count
            
            return [], 0
        
        except Exception as e:
            logger.error(f"Error getting all requests: {e}")
            return [], 0
    
    @staticmethod
    def create(request_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建新请求"""
        try:
            result = db_service.execute_query(
                'user_requests',
                'insert',
                data=request_data,
                use_service_key=True  # 使用service key绕过RLS策略
            )

            if result.data and len(result.data) > 0:
                return result.data[0]
            return None

        except Exception as e:
            logger.error(f"Error creating request: {e}")
            return None
    
    @staticmethod
    def update_status(request_id: str, new_status: str,
                     processed_by: str, admin_notes: str = '') -> bool:
        """更新请求状态"""
        try:
            update_data = {
                'status': new_status,
                'processed_by': processed_by,
                'processed_at': datetime.utcnow().isoformat(),
                'admin_notes': admin_notes
            }

            result = db_service.execute_query(
                'user_requests',
                'update',
                data=update_data,
                filters={'id': request_id},
                use_service_key=True  # 使用service key绕过RLS策略
            )

            return result.data is not None

        except Exception as e:
            logger.error(f"Error updating request status {request_id}: {e}")
            return False
    
    @staticmethod
    def delete(request_id: str) -> bool:
        """删除请求"""
        try:
            result = db_service.execute_query(
                'user_requests',
                'delete',
                filters={'id': request_id},
                use_service_key=True  # 使用service key绕过RLS策略
            )

            return result.data is not None

        except Exception as e:
            logger.error(f"Error deleting request {request_id}: {e}")
            return False
    
    @staticmethod
    def get_pending_count() -> int:
        """获取待处理请求数量"""
        try:
            result = db_service.execute_query(
                'user_requests', 
                'select', 
                filters={'status': 'pending'}
            )
            
            return len(result.data) if result.data else 0
        
        except Exception as e:
            logger.error(f"Error getting pending count: {e}")
            return 0
    
    @staticmethod
    def get_recent_requests(limit: int = 5) -> List[Dict[str, Any]]:
        """获取最近的请求"""
        try:
            result = db_service.execute_query('user_requests', 'select')
            
            if result.data:
                sorted_data = sorted(result.data, key=lambda x: x['created_at'], reverse=True)
                return sorted_data[:limit]
            
            return []
        
        except Exception as e:
            logger.error(f"Error getting recent requests: {e}")
            return []
    
    @staticmethod
    def get_by_status(status: str) -> List[Dict[str, Any]]:
        """根据状态获取请求列表"""
        try:
            result = db_service.execute_query(
                'user_requests', 
                'select', 
                filters={'status': status}
            )
            
            if result.data:
                return sorted(result.data, key=lambda x: x['created_at'], reverse=True)
            
            return []
        
        except Exception as e:
            logger.error(f"Error getting requests by status {status}: {e}")
            return []
